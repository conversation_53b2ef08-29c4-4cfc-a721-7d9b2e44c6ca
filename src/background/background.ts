import { StorageManager } from '../utils/storage';
import { WorkspaceSwitcher } from '../utils/workspaceSwitcher';
import { WindowManager } from '../utils/windowManager';
import { COMMANDS } from '../utils/constants';

/**
 * Chrome扩展后台脚本
 */
class BackgroundService {
  constructor() {
    this.init();
  }

  /**
   * 初始化后台服务
   */
  private async init(): Promise<void> {
    // 设置侧边栏行为
    await this.setupSidePanel();

    // 监听命令
    this.setupCommandListeners();

    // 监听标签页事件
    this.setupTabListeners();

    // 监听存储变化
    this.setupStorageListeners();

    // 监听扩展生命周期事件
    this.setupLifecycleListeners();

    // 初始化默认数据
    await this.initializeDefaultData();

    // 清理可能遗留的隐藏窗口
    await this.cleanupOnStartup();

    console.log('WorkSpace Pro background service initialized');
  }

  /**
   * 设置侧边栏
   */
  private async setupSidePanel(): Promise<void> {
    try {
      // 设置侧边栏在点击扩展图标时打开
      await chrome.sidePanel.setPanelBehavior({
        openPanelOnActionClick: true
      });
    } catch (error) {
      console.error('Failed to setup side panel:', error);
    }
  }

  /**
   * 设置命令监听器
   */
  private setupCommandListeners(): void {
    chrome.commands.onCommand.addListener(async (command) => {
      console.log('Command received:', command);
      
      try {
        switch (command) {
          case COMMANDS.SWITCH_WORKSPACE_1:
            await this.switchToWorkspaceByIndex(0);
            break;
          case COMMANDS.SWITCH_WORKSPACE_2:
            await this.switchToWorkspaceByIndex(1);
            break;
          case COMMANDS.SWITCH_WORKSPACE_3:
            await this.switchToWorkspaceByIndex(2);
            break;
          case COMMANDS.TOGGLE_SIDEPANEL:
            await this.toggleSidePanel();
            break;
          default:
            console.log('Unknown command:', command);
        }
      } catch (error) {
        console.error('Error handling command:', command, error);
      }
    });
  }

  /**
   * 设置标签页监听器
   */
  private setupTabListeners(): void {
    // 监听标签页激活
    chrome.tabs.onActivated.addListener(async (_activeInfo) => {
      try {
        // 检测当前应该激活的工作区
        const detectedResult = await WorkspaceSwitcher.detectActiveWorkspace();
        if (detectedResult.success && detectedResult.data) {
          const currentResult = await WorkspaceSwitcher.getCurrentWorkspace();
          if (currentResult.success && 
              (!currentResult.data || currentResult.data.id !== detectedResult.data.id)) {
            // 自动切换到检测到的工作区（不关闭其他标签页）
            await WorkspaceSwitcher.switchToWorkspace(detectedResult.data.id, {
              closeOtherTabs: false,
              focusFirstTab: false,
            });
          }
        }
      } catch (error) {
        console.error('Error handling tab activation:', error);
      }
    });

    // 监听标签页更新
    chrome.tabs.onUpdated.addListener(async (_tabId, changeInfo, tab) => {
      if (changeInfo.status === 'complete' && tab.url) {
        try {
          // 可以在这里添加自动检测逻辑
          console.log('Tab updated:', tab.url);
        } catch (error) {
          console.error('Error handling tab update:', error);
        }
      }
    });

    // 监听标签页创建
    chrome.tabs.onCreated.addListener(async (tab) => {
      try {
        console.log('Tab created:', tab.url);
      } catch (error) {
        console.error('Error handling tab creation:', error);
      }
    });
  }

  /**
   * 设置存储监听器
   */
  private setupStorageListeners(): void {
    StorageManager.onChanged((changes) => {
      console.log('Storage changed:', changes);

      // 通知侧边栏更新
      this.notifySidePanelUpdate(changes);
    });
  }

  /**
   * 设置扩展生命周期监听器
   */
  private setupLifecycleListeners(): void {
    // 监听扩展启动
    chrome.runtime.onStartup.addListener(async () => {
      console.log('Extension startup detected');
      await this.cleanupOnStartup();
    });

    // 监听扩展安装
    chrome.runtime.onInstalled.addListener(async (details) => {
      console.log('Extension installed/updated:', details.reason);
      if (details.reason === 'install' || details.reason === 'update') {
        await this.cleanupOnStartup();
      }
    });

    // 监听扩展挂起（Chrome可能会挂起Service Worker）
    chrome.runtime.onSuspend.addListener(async () => {
      console.log('Extension suspending, cleaning up...');
      await this.cleanupOnSuspend();
    });
  }

  /**
   * 初始化默认数据
   */
  private async initializeDefaultData(): Promise<void> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (workspacesResult.success && workspacesResult.data!.length === 0) {
        console.log('No workspaces found, creating default workspace templates');
        
        // 可以选择性地创建一些默认工作区
        // 这里暂时不自动创建，让用户自己选择
      }
    } catch (error) {
      console.error('Error initializing default data:', error);
    }
  }

  /**
   * 根据索引切换工作区
   */
  private async switchToWorkspaceByIndex(index: number): Promise<void> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        console.error('Failed to get workspaces:', workspacesResult.error);
        return;
      }

      const workspaces = workspacesResult.data!;
      if (index >= 0 && index < workspaces.length) {
        const workspace = workspaces[index];
        const result = await WorkspaceSwitcher.switchToWorkspace(workspace.id);
        if (result.success) {
          console.log(`Switched to workspace: ${workspace.name}`);
          this.showNotification(`切换到工作区: ${workspace.name}`, workspace.icon);
        } else {
          console.error('Failed to switch workspace:', result.error);
        }
      } else {
        console.log(`No workspace at index ${index}`);
      }
    } catch (error) {
      console.error('Error switching workspace by index:', error);
    }
  }

  /**
   * 切换侧边栏显示状态
   */
  private async toggleSidePanel(): Promise<void> {
    try {
      // 获取当前活跃的标签页
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length > 0) {
        const tabId = tabs[0].id!;
        // 这里可以实现侧边栏的切换逻辑
        // 由于Chrome API限制，我们主要依赖用户点击扩展图标
        console.log('Toggle side panel for tab:', tabId);
      }
    } catch (error) {
      console.error('Error toggling side panel:', error);
    }
  }

  /**
   * 显示通知
   */
  private showNotification(message: string, icon?: string): void {
    try {
      // 创建简单的通知
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon-48.png',
        title: 'WorkSpace Pro',
        message: `${icon || '🚀'} ${message}`,
      });
      console.log('Notification shown:', message);
    } catch (error) {
      console.error('Error showing notification:', error);
    }
  }

  /**
   * 启动时清理
   */
  private async cleanupOnStartup(): Promise<void> {
    try {
      console.log('Performing startup cleanup...');

      // 清理可能遗留的隐藏窗口
      await WorkspaceSwitcher.cleanupAllHiddenWindows();

      console.log('Startup cleanup completed');
    } catch (error) {
      console.error('Error during startup cleanup:', error);
    }
  }

  /**
   * 挂起时清理
   */
  private async cleanupOnSuspend(): Promise<void> {
    try {
      console.log('Performing suspend cleanup...');

      // 清理所有隐藏窗口
      await WorkspaceSwitcher.cleanupAllHiddenWindows();

      console.log('Suspend cleanup completed');
    } catch (error) {
      console.error('Error during suspend cleanup:', error);
    }
  }

  /**
   * 通知侧边栏更新
   */
  private notifySidePanelUpdate(_changes: { [key: string]: chrome.storage.StorageChange }): void {
    try {
      // 这里可以通过消息传递通知侧边栏更新
      // 由于侧边栏是独立的页面，我们主要依赖存储监听
      console.log('Notifying side panel of storage changes');
    } catch (error) {
      console.error('Error notifying side panel:', error);
    }
  }
}

// 初始化后台服务
new BackgroundService();
