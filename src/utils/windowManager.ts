import { 
  WindowInfo, 
  TabInfo, 
  TabMoveOptions,
  OperationResult 
} from '@/types/workspace';
import { ERROR_CODES } from './constants';

/**
 * 窗口管理类 - 处理隐藏窗口和标签页移动
 */
export class WindowManager {
  // 隐藏窗口的位置配置
  private static readonly HIDDEN_WINDOW_CONFIG = {
    left: -3000,
    top: -3000,
    width: 800,
    height: 600,
    state: 'minimized' as chrome.windows.WindowState,
    type: 'normal' as chrome.windows.WindowType,
    focused: false,
  };

  /**
   * 获取当前主窗口
   */
  static async getCurrentWindow(): Promise<OperationResult<WindowInfo>> {
    try {
      const window = await chrome.windows.getCurrent();
      const windowInfo: WindowInfo = {
        id: window.id!,
        focused: window.focused,
        incognito: window.incognito,
        state: window.state!,
        type: window.type!,
        left: window.left,
        top: window.top,
        width: window.width,
        height: window.height,
      };

      return { success: true, data: windowInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get current window',
          details: error,
        },
      };
    }
  }

  /**
   * 创建隐藏窗口
   */
  static async createHiddenWindow(): Promise<OperationResult<number>> {
    try {
      console.log('Creating hidden window...');
      
      // 创建一个最小化的离屏窗口
      const window = await chrome.windows.create({
        ...this.HIDDEN_WINDOW_CONFIG,
        url: 'chrome://newtab/', // 创建一个新标签页
      });

      if (!window.id) {
        throw new Error('Failed to create window - no window ID returned');
      }

      console.log(`Hidden window created with ID: ${window.id}`);
      
      // 确保窗口是最小化和隐藏的
      await chrome.windows.update(window.id, {
        state: 'minimized',
        focused: false,
      });

      return { success: true, data: window.id };
    } catch (error) {
      console.error('Failed to create hidden window:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to create hidden window',
          details: error,
        },
      };
    }
  }

  /**
   * 检查窗口是否存在
   */
  static async windowExists(windowId: number): Promise<boolean> {
    try {
      await chrome.windows.get(windowId);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 获取或创建隐藏窗口
   */
  static async getOrCreateHiddenWindow(existingWindowId?: number): Promise<OperationResult<number>> {
    try {
      // 如果已有窗口ID，先检查是否还存在
      if (existingWindowId && await this.windowExists(existingWindowId)) {
        console.log(`Using existing hidden window: ${existingWindowId}`);
        return { success: true, data: existingWindowId };
      }

      // 创建新的隐藏窗口
      return await this.createHiddenWindow();
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get or create hidden window',
          details: error,
        },
      };
    }
  }

  /**
   * 关闭隐藏窗口
   */
  static async closeHiddenWindow(windowId: number): Promise<OperationResult<void>> {
    try {
      if (await this.windowExists(windowId)) {
        console.log(`Closing hidden window: ${windowId}`);
        await chrome.windows.remove(windowId);
        console.log(`Hidden window ${windowId} closed successfully`);
      }
      return { success: true };
    } catch (error) {
      console.error(`Failed to close hidden window ${windowId}:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to close hidden window',
          details: error,
        },
      };
    }
  }

  /**
   * 获取窗口中的所有标签页
   */
  static async getWindowTabs(windowId: number): Promise<OperationResult<TabInfo[]>> {
    try {
      const tabs = await chrome.tabs.query({ windowId });
      const tabInfos: TabInfo[] = tabs.map(tab => ({
        id: tab.id!,
        url: tab.url || '',
        title: tab.title || '',
        favicon: tab.favIconUrl || '',
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
      }));

      return { success: true, data: tabInfos };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get window tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 移动标签页到指定窗口（带重试和错误处理）
   */
  static async moveTabsToWindow(
    tabIds: number[],
    targetWindowId: number,
    options: TabMoveOptions = {}
  ): Promise<OperationResult<void>> {
    try {
      if (tabIds.length === 0) {
        return { success: true };
      }

      console.log(`Moving ${tabIds.length} tabs to window ${targetWindowId}`);

      // 等待一小段时间，避免与用户操作冲突
      await new Promise(resolve => setTimeout(resolve, 100));

      // 批量移动标签页，带重试机制
      let movedTabs;
      let retries = 3;

      for (let attempt = 1; attempt <= retries; attempt++) {
        try {
          movedTabs = await chrome.tabs.move(tabIds, {
            windowId: targetWindowId,
            index: -1, // 移动到窗口末尾
          });
          break; // 成功则跳出重试循环
        } catch (error: any) {
          const errorMessage = error?.message || '';

          if ((errorMessage.includes('user may be dragging') ||
               errorMessage.includes('Tabs cannot be edited right now')) &&
              attempt < retries) {
            console.warn(`Tab move blocked (attempt ${attempt}/${retries}), retrying...`);
            await new Promise(resolve => setTimeout(resolve, 300 * attempt));
            continue;
          }

          throw error; // 重新抛出错误
        }
      }

      console.log(`Successfully moved ${Array.isArray(movedTabs) ? movedTabs.length : 1} tabs`);

      // 如果需要保持固定状态，使用改进的pinTab方法
      if (options.preservePinned) {
        for (const tabId of tabIds) {
          try {
            const tab = await chrome.tabs.get(tabId);
            if (tab.pinned) {
              // 使用带重试的pinTab方法
              const { TabManager } = await import('./tabs');
              await TabManager.pinTab(tabId, true);
            }
          } catch (error) {
            console.warn(`Failed to preserve pinned state for tab ${tabId}:`, error);
          }
        }
      }

      return { success: true };
    } catch (error) {
      console.error('Failed to move tabs to window:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to move tabs to window',
          details: error,
        },
      };
    }
  }

  /**
   * 确保窗口至少有一个标签页
   */
  static async ensureWindowHasTab(windowId: number): Promise<OperationResult<void>> {
    try {
      const tabsResult = await this.getWindowTabs(windowId);
      if (!tabsResult.success) return tabsResult;

      const tabs = tabsResult.data!;
      console.log(`Window ${windowId} currently has ${tabs.length} tabs`);

      // 如果窗口没有标签页，创建一个新标签页
      if (tabs.length === 0) {
        console.log(`Creating new tab for empty window ${windowId}`);
        const newTab = await chrome.tabs.create({
          windowId,
          url: 'chrome://newtab/',
          active: true,
        });
        console.log(`Created new tab ${newTab.id} in window ${windowId}`);
      } else {
        console.log(`Window ${windowId} already has tabs, no action needed`);
      }

      return { success: true };
    } catch (error) {
      console.error(`Failed to ensure window ${windowId} has tab:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to ensure window has tab',
          details: error,
        },
      };
    }
  }

  /**
   * 清理所有隐藏窗口
   */
  static async cleanupAllHiddenWindows(): Promise<OperationResult<void>> {
    try {
      console.log('Cleaning up all hidden windows...');
      
      const windows = await chrome.windows.getAll();
      const hiddenWindows = windows.filter(window => 
        window.left === this.HIDDEN_WINDOW_CONFIG.left && 
        window.top === this.HIDDEN_WINDOW_CONFIG.top
      );

      for (const window of hiddenWindows) {
        if (window.id) {
          await this.closeHiddenWindow(window.id);
        }
      }

      console.log(`Cleaned up ${hiddenWindows.length} hidden windows`);
      return { success: true };
    } catch (error) {
      console.error('Failed to cleanup hidden windows:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to cleanup hidden windows',
          details: error,
        },
      };
    }
  }
}
