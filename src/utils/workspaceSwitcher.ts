import {
  WorkSpace,
  WorkspaceSwitchOptions,
  OperationResult
} from '@/types/workspace';
import { StorageManager } from './storage';
import { TabManager } from './tabs';
import { WorkspaceManager } from './workspace';
import { WindowManager } from './windowManager';
import { ERROR_CODES } from './constants';

/**
 * 工作区切换管理类
 */
export class WorkspaceSwitcher {
  /**
   * 切换到指定工作区 - 使用隐藏窗口隔离机制
   */
  static async switchToWorkspace(
    workspaceId: string,
    options: WorkspaceSwitchOptions = {}
  ): Promise<OperationResult<void>> {
    try {
      console.log(`Starting workspace switch to: ${workspaceId}`);

      // 获取目标工作区
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) return workspaceResult;

      const targetWorkspace = workspaceResult.data!;

      // 获取当前工作区
      const currentWorkspaceResult = await this.getCurrentWorkspace();
      const currentWorkspace = currentWorkspaceResult.success ? currentWorkspaceResult.data : null;

      // 如果切换到相同工作区，直接返回
      if (currentWorkspace && currentWorkspace.id === workspaceId) {
        console.log('Already in target workspace, no switch needed');
        return { success: true };
      }

      // 获取设置
      const settingsResult = await StorageManager.getSettings();
      if (!settingsResult.success) return settingsResult;

      const settings = settingsResult.data!;

      // 合并选项和设置
      const switchOptions: WorkspaceSwitchOptions = {
        closeOtherTabs: options.closeOtherTabs ?? settings.autoCloseOtherTabs,
        preserveUserOpenedTabs: options.preserveUserOpenedTabs ?? settings.preserveUserOpenedTabs,
        focusFirstTab: options.focusFirstTab ?? true,
      };

      // 始终使用隐藏窗口模式进行切换
      const switchResult = await this.switchWithHiddenWindows(currentWorkspace, targetWorkspace, switchOptions);
      if (!switchResult.success) return switchResult;

      // 设置为活跃工作区
      await StorageManager.setActiveWorkspaceId(workspaceId);

      // 更新工作区状态
      const workspacesResult = await StorageManager.getWorkspaces();
      if (workspacesResult.success) {
        const workspaces = workspacesResult.data!;
        workspaces.forEach(w => {
          w.isActive = w.id === workspaceId;
        });
        await StorageManager.saveWorkspaces(workspaces);
      }

      console.log(`Workspace switch completed successfully to: ${targetWorkspace.name}`);
      return { success: true };
    } catch (error) {
      console.error('Failed to switch workspace:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to switch workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 使用隐藏窗口模式切换工作区
   */
  private static async switchWithHiddenWindows(
    currentWorkspace: WorkSpace | null,
    targetWorkspace: WorkSpace,
    options: WorkspaceSwitchOptions
  ): Promise<OperationResult<void>> {
    try {
      console.log(`=== Starting workspace switch: ${currentWorkspace?.name || 'None'} → ${targetWorkspace.name} ===`);

      // 获取当前主窗口
      const currentWindowResult = await WindowManager.getCurrentWindow();
      if (!currentWindowResult.success) return currentWindowResult;

      const currentWindow = currentWindowResult.data!;
      console.log(`Main window ID: ${currentWindow.id}`);

      // 记录切换前的状态
      const beforeTabsResult = await WindowManager.getWindowTabs(currentWindow.id);
      if (beforeTabsResult.success) {
        const beforeTabs = beforeTabsResult.data!;
        console.log(`Before switch - Main window has ${beforeTabs.length} tabs:`,
          beforeTabs.map(tab => ({ id: tab.id, url: tab.url, title: tab.title })));
      }

      // 1. 如果有当前工作区，将其标签页移动到隐藏窗口
      if (currentWorkspace) {
        console.log(`Step 1: Moving current workspace (${currentWorkspace.name}) tabs to hidden window`);
        const moveToHiddenResult = await this.moveCurrentTabsToHiddenWindow(currentWorkspace, currentWindow.id);
        if (!moveToHiddenResult.success) {
          console.error('Failed to move current tabs to hidden window:', moveToHiddenResult.error);
          // 继续执行，不要因为这个错误中断
        }
      } else {
        console.log('Step 1: No current workspace, skipping tab move to hidden window');
      }

      // 2. 从目标工作区的隐藏窗口移动标签页到主窗口
      console.log(`Step 2: Moving target workspace (${targetWorkspace.name}) tabs from hidden window`);
      const moveFromHiddenResult = await this.moveTabsFromHiddenWindow(targetWorkspace, currentWindow.id);
      if (!moveFromHiddenResult.success) {
        console.error('Failed to move tabs from hidden window:', moveFromHiddenResult.error);
        // 如果移动失败，尝试创建工作区网站
        console.log('Fallback: Creating workspace websites');
        await this.openWorkspaceWebsites(targetWorkspace);
      }

      // 3. 确保主窗口至少有一个标签页
      console.log('Step 3: Ensuring main window has at least one tab');
      await WindowManager.ensureWindowHasTab(currentWindow.id);

      // 4. 验证最终状态
      const afterTabsResult = await WindowManager.getWindowTabs(currentWindow.id);
      if (afterTabsResult.success) {
        const afterTabs = afterTabsResult.data!;
        console.log(`After switch - Main window has ${afterTabs.length} tabs:`,
          afterTabs.map(tab => ({ id: tab.id, url: tab.url, title: tab.title })));
      }

      // 5. 如果需要，聚焦到第一个标签页
      if (options.focusFirstTab) {
        console.log('Step 4: Focusing first tab');
        const tabsResult = await WindowManager.getWindowTabs(currentWindow.id);
        if (tabsResult.success && tabsResult.data!.length > 0) {
          const firstTab = tabsResult.data![0];
          // 使用带重试的激活方法
          const activateResult = await TabManager.activateTab(firstTab.id);
          if (!activateResult.success) {
            console.warn('Failed to activate first tab, but continuing:', activateResult.error);
          }
        }
      }

      console.log(`=== Workspace switch completed: ${targetWorkspace.name} ===`);
      return { success: true };
    } catch (error) {
      console.error('Error in hidden windows switch:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to switch with hidden windows',
          details: error,
        },
      };
    }
  }



  /**
   * 打开工作区的所有网站
   */
  private static async openWorkspaceWebsites(workspace: WorkSpace): Promise<OperationResult<void>> {
    try {
      console.log(`Opening workspace: ${workspace.name} with ${workspace.websites.length} websites`);

      for (const website of workspace.websites) {
        try {
          console.log(`Processing website: ${website.title} (${website.url})`);

          // 检查是否已经打开
          const existingTabResult = await TabManager.findTabByUrl(website.url);

          if (existingTabResult.success && existingTabResult.data) {
            // 标签页已存在，激活并固定
            const tab = existingTabResult.data;
            console.log(`Found existing tab for ${website.title}, activating and pinning`);

            await TabManager.activateTab(tab.id);

            // 总是固定工作区的标签页（除非明确设置为不固定）
            if (!tab.isPinned) {
              await TabManager.pinTab(tab.id, true);
              console.log(`Pinned tab for ${website.title}`);
            }
          } else {
            // 创建新标签页（不自动固定）
            console.log(`Creating new tab for ${website.title}`);
            const newTabResult = await TabManager.createTab(website.url, false, false);
            if (newTabResult.success) {
              console.log(`Created new tab for ${website.title}`);
            } else {
              console.error(`Failed to create tab for ${website.title}:`, newTabResult.error);
            }
          }
        } catch (error) {
          console.error(`Error processing website ${website.title}:`, error);
          // 继续处理其他网站，不要因为一个失败而停止
        }
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to open workspace websites',
          details: error,
        },
      };
    }
  }

  /**
   * 将当前窗口的标签页移动到工作区的隐藏窗口
   */
  private static async moveCurrentTabsToHiddenWindow(
    workspace: WorkSpace,
    currentWindowId: number
  ): Promise<OperationResult<void>> {
    try {
      console.log(`Moving current tabs to hidden window for workspace: ${workspace.name}`);

      // 获取当前窗口的所有标签页
      const tabsResult = await WindowManager.getWindowTabs(currentWindowId);
      if (!tabsResult.success) return tabsResult;

      const tabs = tabsResult.data!;
      console.log(`Found ${tabs.length} tabs in current window:`, tabs.map(tab => ({ id: tab.id, url: tab.url, title: tab.title })));

      if (tabs.length === 0) {
        console.log('No tabs to move');
        return { success: true };
      }

      // 获取或创建隐藏窗口
      const hiddenWindowResult = await WindowManager.getOrCreateHiddenWindow(workspace.hiddenWindowId);
      if (!hiddenWindowResult.success) return hiddenWindowResult;

      const hiddenWindowId = hiddenWindowResult.data!;

      // 更新工作区的隐藏窗口ID
      if (workspace.hiddenWindowId !== hiddenWindowId) {
        workspace.hiddenWindowId = hiddenWindowId;
        await this.updateWorkspaceHiddenWindowId(workspace.id, hiddenWindowId);
      }

      // 移动所有标签页到隐藏窗口
      // 关键修复：移动所有标签页，确保完全隔离
      const tabIds = tabs.map(tab => tab.id);
      console.log(`Moving all ${tabIds.length} tabs to hidden window ${hiddenWindowId}`);

      const moveResult = await WindowManager.moveTabsToWindow(tabIds, hiddenWindowId, {
        preserveOrder: true,
        preservePinned: true,
      });

      if (!moveResult.success) return moveResult;

      // 确保主窗口有一个新标签页
      await WindowManager.ensureWindowHasTab(currentWindowId);

      console.log(`Successfully moved all ${tabIds.length} tabs to hidden window ${hiddenWindowId}`);
      return { success: true };
    } catch (error) {
      console.error('Failed to move current tabs to hidden window:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to move current tabs to hidden window',
          details: error,
        },
      };
    }
  }

  /**
   * 从工作区的隐藏窗口移动标签页到主窗口
   */
  private static async moveTabsFromHiddenWindow(
    workspace: WorkSpace,
    targetWindowId: number
  ): Promise<OperationResult<void>> {
    try {
      console.log(`Moving tabs from hidden window for workspace: ${workspace.name}`);

      // 如果工作区没有隐藏窗口，创建工作区网站
      if (!workspace.hiddenWindowId) {
        console.log('No hidden window found, creating workspace websites');
        return await this.openWorkspaceWebsites(workspace);
      }

      // 检查隐藏窗口是否还存在
      const windowExists = await WindowManager.windowExists(workspace.hiddenWindowId);
      if (!windowExists) {
        console.log('Hidden window no longer exists, creating workspace websites');
        workspace.hiddenWindowId = undefined;
        await this.updateWorkspaceHiddenWindowId(workspace.id, undefined);
        return await this.openWorkspaceWebsites(workspace);
      }

      // 获取隐藏窗口的标签页
      const tabsResult = await WindowManager.getWindowTabs(workspace.hiddenWindowId);
      if (!tabsResult.success) return tabsResult;

      const tabs = tabsResult.data!;
      console.log(`Found ${tabs.length} tabs in hidden window ${workspace.hiddenWindowId}:`,
        tabs.map(tab => ({ id: tab.id, url: tab.url, title: tab.title })));

      if (tabs.length === 0) {
        console.log('No tabs in hidden window, creating workspace websites');
        return await this.openWorkspaceWebsites(workspace);
      }

      // 验证目标窗口当前状态
      const currentTabsResult = await WindowManager.getWindowTabs(targetWindowId);
      if (currentTabsResult.success) {
        const currentTabs = currentTabsResult.data!;
        console.log(`Target window ${targetWindowId} currently has ${currentTabs.length} tabs:`,
          currentTabs.map(tab => ({ id: tab.id, url: tab.url, title: tab.title })));
      }

      // 移动所有标签页到主窗口
      const tabIds = tabs.map(tab => tab.id);
      console.log(`Moving ${tabIds.length} tabs from hidden window ${workspace.hiddenWindowId} to main window ${targetWindowId}`);

      const moveResult = await WindowManager.moveTabsToWindow(tabIds, targetWindowId, {
        preserveOrder: true,
        preservePinned: true,
      });

      if (!moveResult.success) return moveResult;

      // 验证移动后的状态
      const finalTabsResult = await WindowManager.getWindowTabs(targetWindowId);
      if (finalTabsResult.success) {
        const finalTabs = finalTabsResult.data!;
        console.log(`After move, main window ${targetWindowId} has ${finalTabs.length} tabs:`,
          finalTabs.map(tab => ({ id: tab.id, url: tab.url, title: tab.title })));
      }

      console.log(`Successfully moved ${tabs.length} tabs from hidden window to main window`);
      return { success: true };
    } catch (error) {
      console.error('Failed to move tabs from hidden window:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to move tabs from hidden window',
          details: error,
        },
      };
    }
  }

  /**
   * 更新工作区的隐藏窗口ID
   */
  private static async updateWorkspaceHiddenWindowId(
    workspaceId: string,
    hiddenWindowId: number | undefined
  ): Promise<OperationResult<void>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) return workspacesResult;

      const workspaces = workspacesResult.data!;
      const workspace = workspaces.find(w => w.id === workspaceId);

      if (workspace) {
        workspace.hiddenWindowId = hiddenWindowId;
        workspace.updatedAt = Date.now();
        await StorageManager.saveWorkspaces(workspaces);
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to update workspace hidden window ID',
          details: error,
        },
      };
    }
  }

  /**
   * 关闭非工作区相关的标签页
   */
  private static async closeNonWorkspaceTabs(
    workspace: WorkSpace,
    preserveUserOpenedTabs: boolean
  ): Promise<OperationResult<void>> {
    try {
      console.log(`Closing non-workspace tabs. Preserve user opened tabs: ${preserveUserOpenedTabs}`);

      const nonRelatedTabsResult = await TabManager.getNonWorkspaceRelatedTabs(workspace);
      if (!nonRelatedTabsResult.success) return nonRelatedTabsResult;

      const nonRelatedTabs = nonRelatedTabsResult.data!;
      console.log(`Found ${nonRelatedTabs.length} non-workspace related tabs`);
      
      // 过滤需要关闭的标签页
      const tabsToClose = nonRelatedTabs.filter(tab => {
        // 不关闭扩展页面和特殊页面
        if (tab.url.startsWith('chrome://') ||
            tab.url.startsWith('chrome-extension://') ||
            tab.url.startsWith('moz-extension://')) {
          return false;
        }

        // 如果设置了保留用户手动打开的标签页
        if (preserveUserOpenedTabs) {
          // 保留所有非固定的标签页（认为是用户手动打开的）
          // 只关闭固定的标签页（认为是之前工作区管理的）
          return tab.isPinned;
        }

        // 如果没有设置保留，则关闭所有非工作区相关的标签页
        return true;
      });

      // 关闭标签页
      if (tabsToClose.length > 0) {
        console.log(`Closing ${tabsToClose.length} tabs:`, tabsToClose.map(tab => ({ id: tab.id, url: tab.url, isPinned: tab.isPinned })));
        const tabIds = tabsToClose.map(tab => tab.id);
        await TabManager.closeTabs(tabIds);
        console.log(`Successfully closed ${tabsToClose.length} tabs`);
      } else {
        console.log('No tabs to close');
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to close non-workspace tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 获取当前活跃的工作区
   */
  static async getCurrentWorkspace(): Promise<OperationResult<WorkSpace | null>> {
    try {
      const activeIdResult = await StorageManager.getActiveWorkspaceId();
      if (!activeIdResult.success) return activeIdResult;

      const activeId = activeIdResult.data;
      if (!activeId) {
        return { success: true, data: null };
      }

      const workspaceResult = await StorageManager.getWorkspace(activeId);
      if (!workspaceResult.success) {
        // 如果工作区不存在，清除活跃状态
        await StorageManager.setActiveWorkspaceId(null);
        return { success: true, data: null };
      }

      return { success: true, data: workspaceResult.data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get current workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 智能检测当前应该激活的工作区
   */
  static async detectActiveWorkspace(): Promise<OperationResult<WorkSpace | null>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) return workspacesResult;

      const workspaces = workspacesResult.data!;
      if (workspaces.length === 0) {
        return { success: true, data: null };
      }

      const activeTabResult = await TabManager.getActiveTab();
      if (!activeTabResult.success) {
        return { success: true, data: null };
      }

      const activeTab = activeTabResult.data!;

      // 查找包含当前活跃标签页URL的工作区
      const matchingWorkspace = workspaces.find(workspace =>
        workspace.websites.some(website => 
          activeTab.url.startsWith(website.url)
        )
      );

      return { success: true, data: matchingWorkspace || null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to detect active workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 添加当前标签页到工作区
   */
  static async addCurrentTabToWorkspace(workspaceId: string): Promise<OperationResult<void>> {
    try {
      const activeTabResult = await TabManager.getActiveTab();
      if (!activeTabResult.success) return activeTabResult;

      const activeTab = activeTabResult.data!;

      // 添加到工作区
      const addResult = await WorkspaceManager.addWebsite(
        workspaceId, 
        activeTab.url, 
        {
          title: activeTab.title,
          favicon: activeTab.favicon,
          pinTab: activeTab.isPinned,
        }
      );

      return addResult;
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to add current tab to workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 快速切换到下一个工作区
   */
  static async switchToNextWorkspace(): Promise<OperationResult<void>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) return workspacesResult;

      const workspaces = workspacesResult.data!;
      if (workspaces.length === 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: 'No workspaces available',
          },
        };
      }

      const currentResult = await this.getCurrentWorkspace();
      if (!currentResult.success) return currentResult;

      const currentWorkspace = currentResult.data;
      let nextIndex = 0;

      if (currentWorkspace) {
        const currentIndex = workspaces.findIndex(w => w.id === currentWorkspace.id);
        nextIndex = (currentIndex + 1) % workspaces.length;
      }

      const nextWorkspace = workspaces[nextIndex];
      return await this.switchToWorkspace(nextWorkspace.id);
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to switch to next workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 清理工作区的隐藏窗口
   */
  static async cleanupWorkspaceHiddenWindow(workspaceId: string): Promise<OperationResult<void>> {
    try {
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) return workspaceResult;

      const workspace = workspaceResult.data!;

      if (workspace.hiddenWindowId) {
        console.log(`Cleaning up hidden window ${workspace.hiddenWindowId} for workspace: ${workspace.name}`);

        // 关闭隐藏窗口
        await WindowManager.closeHiddenWindow(workspace.hiddenWindowId);

        // 清除工作区的隐藏窗口ID
        await this.updateWorkspaceHiddenWindowId(workspaceId, undefined);
      }

      return { success: true };
    } catch (error) {
      console.error('Failed to cleanup workspace hidden window:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to cleanup workspace hidden window',
          details: error,
        },
      };
    }
  }

  /**
   * 清理所有工作区的隐藏窗口
   */
  static async cleanupAllHiddenWindows(): Promise<OperationResult<void>> {
    try {
      console.log('Cleaning up all workspace hidden windows...');

      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) return workspacesResult;

      const workspaces = workspacesResult.data!;

      for (const workspace of workspaces) {
        if (workspace.hiddenWindowId) {
          await this.cleanupWorkspaceHiddenWindow(workspace.id);
        }
      }

      // 额外清理任何遗留的隐藏窗口
      await WindowManager.cleanupAllHiddenWindows();

      console.log('All hidden windows cleaned up');
      return { success: true };
    } catch (error) {
      console.error('Failed to cleanup all hidden windows:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to cleanup all hidden windows',
          details: error,
        },
      };
    }
  }
}
