import {
  WorkSpace,
  WorkspaceSwitchOptions,
  OperationResult
} from '@/types/workspace';
import { StorageManager } from './storage';
import { TabManager } from './tabs';
import { WorkspaceManager } from './workspace';
import { WindowManager } from './windowManager';
import { ERROR_CODES } from './constants';

/**
 * 工作区切换管理类
 */
export class WorkspaceSwitcher {
  /**
   * 切换到指定工作区 - 使用隐藏窗口隔离机制
   */
  static async switchToWorkspace(
    workspaceId: string,
    options: WorkspaceSwitchOptions = {}
  ): Promise<OperationResult<void>> {
    try {
      console.log(`🔄 开始切换到工作区: ${workspaceId}`);

      // 获取目标工作区
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) return workspaceResult;

      const targetWorkspace = workspaceResult.data!;
      console.log(`📋 目标工作区: ${targetWorkspace.name}`);

      // 获取当前工作区
      const currentWorkspaceResult = await this.getCurrentWorkspace();
      const currentWorkspace = currentWorkspaceResult.success ? currentWorkspaceResult.data : null;
      console.log(`📋 当前工作区: ${currentWorkspace?.name || '无'}`);

      // 如果切换到相同工作区，直接返回
      if (currentWorkspace && currentWorkspace.id === workspaceId) {
        console.log('⚠️ 已在目标工作区，无需切换');
        return { success: true };
      }

      // 获取设置
      const settingsResult = await StorageManager.getSettings();
      if (!settingsResult.success) return settingsResult;

      const settings = settingsResult.data!;

      // 合并选项和设置
      const switchOptions: WorkspaceSwitchOptions = {
        closeOtherTabs: options.closeOtherTabs ?? settings.autoCloseOtherTabs,
        preserveUserOpenedTabs: options.preserveUserOpenedTabs ?? settings.preserveUserOpenedTabs,
        focusFirstTab: options.focusFirstTab ?? true,
      };

      // 始终使用隐藏窗口模式进行切换
      const switchResult = await this.switchWithHiddenWindows(currentWorkspace, targetWorkspace, switchOptions);
      if (!switchResult.success) return switchResult;

      // 设置为活跃工作区
      await StorageManager.setActiveWorkspaceId(workspaceId);

      // 更新工作区状态
      const workspacesResult = await StorageManager.getWorkspaces();
      if (workspacesResult.success) {
        const workspaces = workspacesResult.data!;
        workspaces.forEach(w => {
          w.isActive = w.id === workspaceId;
        });
        await StorageManager.saveWorkspaces(workspaces);
      }

      console.log(`✅ 工作区切换成功完成: ${targetWorkspace.name}`);
      return { success: true };
    } catch (error) {
      console.error('❌ 工作区切换失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to switch workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 使用隐藏窗口模式切换工作区
   */
  private static async switchWithHiddenWindows(
    currentWorkspace: WorkSpace | null,
    targetWorkspace: WorkSpace,
    options: WorkspaceSwitchOptions
  ): Promise<OperationResult<void>> {
    try {
      console.log(`🔄 === 开始隐藏窗口模式切换: ${currentWorkspace?.name || '无'} → ${targetWorkspace.name} ===`);

      // 获取当前主窗口
      const currentWindowResult = await WindowManager.getCurrentWindow();
      if (!currentWindowResult.success) return currentWindowResult;

      const currentWindow = currentWindowResult.data!;
      console.log(`🪟 主窗口ID: ${currentWindow.id}`);

      // 记录切换前的状态
      const beforeTabsResult = await WindowManager.getWindowTabs(currentWindow.id);
      if (beforeTabsResult.success) {
        const beforeTabs = beforeTabsResult.data!;
        console.log(`📊 切换前状态 - 主窗口有 ${beforeTabs.length} 个标签页:`,
          beforeTabs.map(tab => ({ id: tab.id, url: tab.url, title: tab.title })));
      }

      // 1. 如果有当前工作区，将其标签页移动到隐藏窗口
      if (currentWorkspace) {
        console.log(`📤 步骤1: 将当前工作区 (${currentWorkspace.name}) 的标签页移动到隐藏窗口`);
        const moveToHiddenResult = await this.moveCurrentTabsToHiddenWindow(currentWorkspace, currentWindow.id);
        if (!moveToHiddenResult.success) {
          console.error('❌ 移动当前标签页到隐藏窗口失败:', moveToHiddenResult.error);
          // 继续执行，不要因为这个错误中断
        }
      } else {
        console.log('📤 步骤1: 无当前工作区，跳过标签页移动到隐藏窗口');
      }

      // 2. 验证主窗口已清空（关键隔离验证）
      console.log(`🔍 步骤2: 验证主窗口已清空`);
      const midTabsResult = await WindowManager.getWindowTabs(currentWindow.id);
      if (midTabsResult.success) {
        const midTabs = midTabsResult.data!;
        console.log(`📊 移动后验证 - 主窗口有 ${midTabs.length} 个标签页:`,
          midTabs.map(tab => ({ id: tab.id, url: tab.url, title: tab.title })));

        // 如果主窗口还有多个标签页，说明移动没有完全成功
        if (midTabs.length > 1) {
          console.warn(`⚠️ 警告: 主窗口仍有 ${midTabs.length} 个标签页，隔离可能不完整`);
        }
      }

      // 3. 从目标工作区的隐藏窗口移动标签页到主窗口
      console.log(`📥 步骤3: 从目标工作区 (${targetWorkspace.name}) 的隐藏窗口移动标签页`);
      const moveFromHiddenResult = await this.moveTabsFromHiddenWindow(targetWorkspace, currentWindow.id);
      if (!moveFromHiddenResult.success) {
        console.error('❌ 从隐藏窗口移动标签页失败:', moveFromHiddenResult.error);
        // 如果移动失败，尝试创建工作区网站
        console.log('🔄 回退方案: 创建工作区网站');
        await this.openWorkspaceWebsites(targetWorkspace);
      }

      // 4. 确保主窗口至少有一个标签页
      console.log('🔍 步骤4: 确保主窗口至少有一个标签页');
      await WindowManager.ensureWindowHasTab(currentWindow.id);

      // 5. 验证最终状态
      const afterTabsResult = await WindowManager.getWindowTabs(currentWindow.id);
      if (afterTabsResult.success) {
        const afterTabs = afterTabsResult.data!;
        console.log(`📊 最终状态 - 主窗口有 ${afterTabs.length} 个标签页:`,
          afterTabs.map(tab => ({ id: tab.id, url: tab.url, title: tab.title })));
      }

      // 6. 如果需要，聚焦到第一个标签页
      if (options.focusFirstTab) {
        console.log('🎯 步骤5: 聚焦第一个标签页');
        const tabsResult = await WindowManager.getWindowTabs(currentWindow.id);
        if (tabsResult.success && tabsResult.data!.length > 0) {
          const firstTab = tabsResult.data![0];
          // 使用带重试的激活方法
          const activateResult = await TabManager.activateTab(firstTab.id);
          if (!activateResult.success) {
            console.warn('⚠️ 激活第一个标签页失败，但继续执行:', activateResult.error);
          }
        }
      }

      console.log(`✅ === 工作区切换完成: ${targetWorkspace.name} ===`);
      return { success: true };
    } catch (error) {
      console.error('❌ 隐藏窗口切换错误:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to switch with hidden windows',
          details: error,
        },
      };
    }
  }



  /**
   * 打开工作区的所有网站
   */
  private static async openWorkspaceWebsites(workspace: WorkSpace): Promise<OperationResult<void>> {
    try {
      console.log(`🌐 打开工作区网站: ${workspace.name}，包含 ${workspace.websites.length} 个网站`);

      for (const website of workspace.websites) {
        try {
          console.log(`🔗 处理网站: ${website.title} (${website.url})`);

          // 检查是否已经打开
          const existingTabResult = await TabManager.findTabByUrl(website.url);

          if (existingTabResult.success && existingTabResult.data) {
            // 标签页已存在，只激活不固定
            const tab = existingTabResult.data;
            console.log(`✅ 发现现有标签页: ${website.title}，激活中`);

            await TabManager.activateTab(tab.id);
            console.log(`✅ 已激活标签页: ${website.title}`);
          } else {
            // 创建新标签页（不自动固定）
            console.log(`🆕 为 ${website.title} 创建新标签页`);
            const newTabResult = await TabManager.createTab(website.url, false, false);
            if (newTabResult.success) {
              console.log(`✅ 成功创建新标签页: ${website.title}`);
            } else {
              console.error(`❌ 创建标签页失败: ${website.title}:`, newTabResult.error);
            }
          }
        } catch (error) {
          console.error(`Error processing website ${website.title}:`, error);
          // 继续处理其他网站，不要因为一个失败而停止
        }
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to open workspace websites',
          details: error,
        },
      };
    }
  }

  /**
   * 将当前窗口的标签页移动到工作区的隐藏窗口
   */
  private static async moveCurrentTabsToHiddenWindow(
    workspace: WorkSpace,
    currentWindowId: number
  ): Promise<OperationResult<void>> {
    try {
      console.log(`📤 开始移动当前标签页到隐藏窗口 - 工作区: ${workspace.name}`);

      // 获取当前窗口的所有标签页
      const tabsResult = await WindowManager.getWindowTabs(currentWindowId);
      if (!tabsResult.success) return tabsResult;

      const tabs = tabsResult.data!;
      console.log(`📋 当前窗口发现 ${tabs.length} 个标签页:`, tabs.map(tab => ({ id: tab.id, url: tab.url, title: tab.title })));

      if (tabs.length === 0) {
        console.log('⚠️ 没有标签页需要移动');
        return { success: true };
      }

      // 获取或创建隐藏窗口
      console.log(`🔍 获取或创建隐藏窗口 - 当前隐藏窗口ID: ${workspace.hiddenWindowId || '无'}`);
      const hiddenWindowResult = await WindowManager.getOrCreateHiddenWindow(workspace.hiddenWindowId);
      if (!hiddenWindowResult.success) return hiddenWindowResult;

      const hiddenWindowId = hiddenWindowResult.data!;
      console.log(`🪟 隐藏窗口ID: ${hiddenWindowId}`);

      // 更新工作区的隐藏窗口ID
      if (workspace.hiddenWindowId !== hiddenWindowId) {
        console.log(`📝 更新工作区隐藏窗口ID: ${workspace.hiddenWindowId} → ${hiddenWindowId}`);
        workspace.hiddenWindowId = hiddenWindowId;
        await this.updateWorkspaceHiddenWindowId(workspace.id, hiddenWindowId);
      }

      // 移动所有标签页到隐藏窗口
      // 关键修复：移动所有标签页，确保完全隔离
      const tabIds = tabs.map(tab => tab.id);
      console.log(`🚀 开始移动所有 ${tabIds.length} 个标签页到隐藏窗口 ${hiddenWindowId}`);
      console.log(`📋 移动的标签页ID列表:`, tabIds);

      const moveResult = await WindowManager.moveTabsToWindow(tabIds, hiddenWindowId, {
        preserveOrder: true,
        preservePinned: true,
      });

      if (!moveResult.success) {
        console.error('❌ 移动标签页到隐藏窗口失败:', moveResult.error);
        return moveResult;
      }

      // 确保主窗口有一个新标签页
      console.log(`🔍 确保主窗口 ${currentWindowId} 有新标签页`);
      await WindowManager.ensureWindowHasTab(currentWindowId);

      console.log(`✅ 成功移动所有 ${tabIds.length} 个标签页到隐藏窗口 ${hiddenWindowId}`);
      return { success: true };
    } catch (error) {
      console.error('Failed to move current tabs to hidden window:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to move current tabs to hidden window',
          details: error,
        },
      };
    }
  }

  /**
   * 从工作区的隐藏窗口移动标签页到主窗口
   */
  private static async moveTabsFromHiddenWindow(
    workspace: WorkSpace,
    targetWindowId: number
  ): Promise<OperationResult<void>> {
    try {
      console.log(`📥 开始从隐藏窗口移动标签页 - 工作区: ${workspace.name}`);

      // 如果工作区没有隐藏窗口，创建工作区网站
      if (!workspace.hiddenWindowId) {
        console.log('⚠️ 未找到隐藏窗口，创建工作区网站');
        return await this.openWorkspaceWebsites(workspace);
      }

      console.log(`🔍 检查隐藏窗口是否存在 - 隐藏窗口ID: ${workspace.hiddenWindowId}`);
      // 检查隐藏窗口是否还存在
      const windowExists = await WindowManager.windowExists(workspace.hiddenWindowId);
      if (!windowExists) {
        console.log('⚠️ 隐藏窗口不再存在，创建工作区网站');
        workspace.hiddenWindowId = undefined;
        await this.updateWorkspaceHiddenWindowId(workspace.id, undefined);
        return await this.openWorkspaceWebsites(workspace);
      }

      // 获取隐藏窗口的标签页
      console.log(`📋 获取隐藏窗口 ${workspace.hiddenWindowId} 的标签页`);
      const tabsResult = await WindowManager.getWindowTabs(workspace.hiddenWindowId);
      if (!tabsResult.success) return tabsResult;

      const tabs = tabsResult.data!;
      console.log(`📋 隐藏窗口 ${workspace.hiddenWindowId} 中发现 ${tabs.length} 个标签页:`,
        tabs.map(tab => ({ id: tab.id, url: tab.url, title: tab.title })));

      if (tabs.length === 0) {
        console.log('⚠️ 隐藏窗口中没有标签页，创建工作区网站');
        return await this.openWorkspaceWebsites(workspace);
      }

      // 验证目标窗口当前状态
      const currentTabsResult = await WindowManager.getWindowTabs(targetWindowId);
      if (currentTabsResult.success) {
        const currentTabs = currentTabsResult.data!;
        console.log(`📊 目标窗口 ${targetWindowId} 当前有 ${currentTabs.length} 个标签页:`,
          currentTabs.map(tab => ({ id: tab.id, url: tab.url, title: tab.title })));
      }

      // 移动所有标签页到主窗口
      const tabIds = tabs.map(tab => tab.id);
      console.log(`🚀 开始移动 ${tabIds.length} 个标签页从隐藏窗口 ${workspace.hiddenWindowId} 到主窗口 ${targetWindowId}`);
      console.log(`📋 移动的标签页ID列表:`, tabIds);

      const moveResult = await WindowManager.moveTabsToWindow(tabIds, targetWindowId, {
        preserveOrder: true,
        preservePinned: true,
      });

      if (!moveResult.success) {
        console.error('❌ 从隐藏窗口移动标签页到主窗口失败:', moveResult.error);
        return moveResult;
      }

      // 验证移动后的状态
      const finalTabsResult = await WindowManager.getWindowTabs(targetWindowId);
      if (finalTabsResult.success) {
        const finalTabs = finalTabsResult.data!;
        console.log(`📊 移动后状态 - 主窗口 ${targetWindowId} 有 ${finalTabs.length} 个标签页:`,
          finalTabs.map(tab => ({ id: tab.id, url: tab.url, title: tab.title })));
      }

      console.log(`✅ 成功移动 ${tabs.length} 个标签页从隐藏窗口到主窗口`);
      return { success: true };
    } catch (error) {
      console.error('Failed to move tabs from hidden window:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to move tabs from hidden window',
          details: error,
        },
      };
    }
  }

  /**
   * 更新工作区的隐藏窗口ID
   */
  private static async updateWorkspaceHiddenWindowId(
    workspaceId: string,
    hiddenWindowId: number | undefined
  ): Promise<OperationResult<void>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) return workspacesResult;

      const workspaces = workspacesResult.data!;
      const workspace = workspaces.find(w => w.id === workspaceId);

      if (workspace) {
        workspace.hiddenWindowId = hiddenWindowId;
        workspace.updatedAt = Date.now();
        await StorageManager.saveWorkspaces(workspaces);
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to update workspace hidden window ID',
          details: error,
        },
      };
    }
  }

  /**
   * 关闭非工作区相关的标签页
   */
  private static async closeNonWorkspaceTabs(
    workspace: WorkSpace,
    preserveUserOpenedTabs: boolean
  ): Promise<OperationResult<void>> {
    try {
      console.log(`Closing non-workspace tabs. Preserve user opened tabs: ${preserveUserOpenedTabs}`);

      const nonRelatedTabsResult = await TabManager.getNonWorkspaceRelatedTabs(workspace);
      if (!nonRelatedTabsResult.success) return nonRelatedTabsResult;

      const nonRelatedTabs = nonRelatedTabsResult.data!;
      console.log(`Found ${nonRelatedTabs.length} non-workspace related tabs`);
      
      // 过滤需要关闭的标签页
      const tabsToClose = nonRelatedTabs.filter(tab => {
        // 不关闭扩展页面和特殊页面
        if (tab.url.startsWith('chrome://') ||
            tab.url.startsWith('chrome-extension://') ||
            tab.url.startsWith('moz-extension://')) {
          return false;
        }

        // 如果设置了保留用户手动打开的标签页
        if (preserveUserOpenedTabs) {
          // 保留所有非固定的标签页（认为是用户手动打开的）
          // 只关闭固定的标签页（认为是之前工作区管理的）
          return tab.isPinned;
        }

        // 如果没有设置保留，则关闭所有非工作区相关的标签页
        return true;
      });

      // 关闭标签页
      if (tabsToClose.length > 0) {
        console.log(`Closing ${tabsToClose.length} tabs:`, tabsToClose.map(tab => ({ id: tab.id, url: tab.url, isPinned: tab.isPinned })));
        const tabIds = tabsToClose.map(tab => tab.id);
        await TabManager.closeTabs(tabIds);
        console.log(`Successfully closed ${tabsToClose.length} tabs`);
      } else {
        console.log('No tabs to close');
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to close non-workspace tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 获取当前活跃的工作区
   */
  static async getCurrentWorkspace(): Promise<OperationResult<WorkSpace | null>> {
    try {
      const activeIdResult = await StorageManager.getActiveWorkspaceId();
      if (!activeIdResult.success) return activeIdResult;

      const activeId = activeIdResult.data;
      if (!activeId) {
        return { success: true, data: null };
      }

      const workspaceResult = await StorageManager.getWorkspace(activeId);
      if (!workspaceResult.success) {
        // 如果工作区不存在，清除活跃状态
        await StorageManager.setActiveWorkspaceId(null);
        return { success: true, data: null };
      }

      return { success: true, data: workspaceResult.data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get current workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 智能检测当前应该激活的工作区
   */
  static async detectActiveWorkspace(): Promise<OperationResult<WorkSpace | null>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) return workspacesResult;

      const workspaces = workspacesResult.data!;
      if (workspaces.length === 0) {
        return { success: true, data: null };
      }

      const activeTabResult = await TabManager.getActiveTab();
      if (!activeTabResult.success) {
        return { success: true, data: null };
      }

      const activeTab = activeTabResult.data!;

      // 查找包含当前活跃标签页URL的工作区
      const matchingWorkspace = workspaces.find(workspace =>
        workspace.websites.some(website => 
          activeTab.url.startsWith(website.url)
        )
      );

      return { success: true, data: matchingWorkspace || null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to detect active workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 添加当前标签页到工作区
   */
  static async addCurrentTabToWorkspace(workspaceId: string): Promise<OperationResult<void>> {
    try {
      const activeTabResult = await TabManager.getActiveTab();
      if (!activeTabResult.success) return activeTabResult;

      const activeTab = activeTabResult.data!;

      // 添加到工作区
      const addResult = await WorkspaceManager.addWebsite(
        workspaceId, 
        activeTab.url, 
        {
          title: activeTab.title,
          favicon: activeTab.favicon,
          pinTab: activeTab.isPinned,
        }
      );

      return addResult;
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to add current tab to workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 快速切换到下一个工作区
   */
  static async switchToNextWorkspace(): Promise<OperationResult<void>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) return workspacesResult;

      const workspaces = workspacesResult.data!;
      if (workspaces.length === 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: 'No workspaces available',
          },
        };
      }

      const currentResult = await this.getCurrentWorkspace();
      if (!currentResult.success) return currentResult;

      const currentWorkspace = currentResult.data;
      let nextIndex = 0;

      if (currentWorkspace) {
        const currentIndex = workspaces.findIndex(w => w.id === currentWorkspace.id);
        nextIndex = (currentIndex + 1) % workspaces.length;
      }

      const nextWorkspace = workspaces[nextIndex];
      return await this.switchToWorkspace(nextWorkspace.id);
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to switch to next workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 清理工作区的隐藏窗口
   */
  static async cleanupWorkspaceHiddenWindow(workspaceId: string): Promise<OperationResult<void>> {
    try {
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) return workspaceResult;

      const workspace = workspaceResult.data!;

      if (workspace.hiddenWindowId) {
        console.log(`Cleaning up hidden window ${workspace.hiddenWindowId} for workspace: ${workspace.name}`);

        // 关闭隐藏窗口
        await WindowManager.closeHiddenWindow(workspace.hiddenWindowId);

        // 清除工作区的隐藏窗口ID
        await this.updateWorkspaceHiddenWindowId(workspaceId, undefined);
      }

      return { success: true };
    } catch (error) {
      console.error('Failed to cleanup workspace hidden window:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to cleanup workspace hidden window',
          details: error,
        },
      };
    }
  }

  /**
   * 清理所有工作区的隐藏窗口
   */
  static async cleanupAllHiddenWindows(): Promise<OperationResult<void>> {
    try {
      console.log('Cleaning up all workspace hidden windows...');

      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) return workspacesResult;

      const workspaces = workspacesResult.data!;

      for (const workspace of workspaces) {
        if (workspace.hiddenWindowId) {
          await this.cleanupWorkspaceHiddenWindow(workspace.id);
        }
      }

      // 额外清理任何遗留的隐藏窗口
      await WindowManager.cleanupAllHiddenWindows();

      console.log('All hidden windows cleaned up');
      return { success: true };
    } catch (error) {
      console.error('Failed to cleanup all hidden windows:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to cleanup all hidden windows',
          details: error,
        },
      };
    }
  }
}
