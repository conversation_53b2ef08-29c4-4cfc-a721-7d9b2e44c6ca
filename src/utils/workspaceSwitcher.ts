import { 
  WorkSpace, 
  WorkspaceSwitchOptions,
  OperationResult 
} from '@/types/workspace';
import { StorageManager } from './storage';
import { TabManager } from './tabs';
import { WorkspaceManager } from './workspace';
import { ERROR_CODES } from './constants';

/**
 * 工作区切换管理类
 */
export class WorkspaceSwitcher {
  /**
   * 切换到指定工作区
   */
  static async switchToWorkspace(
    workspaceId: string, 
    options: WorkspaceSwitchOptions = {}
  ): Promise<OperationResult<void>> {
    try {
      // 获取目标工作区
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) return workspaceResult;

      const workspace = workspaceResult.data!;

      // 获取设置
      const settingsResult = await StorageManager.getSettings();
      if (!settingsResult.success) return settingsResult;

      const settings = settingsResult.data!;

      // 合并选项和设置
      const switchOptions: WorkspaceSwitchOptions = {
        closeOtherTabs: options.closeOtherTabs ?? settings.autoCloseOtherTabs,
        preserveUserOpenedTabs: options.preserveUserOpenedTabs ?? settings.preserveUserOpenedTabs,
        focusFirstTab: options.focusFirstTab ?? true,
      };

      // 1. 如果需要关闭其他标签页，先处理
      if (switchOptions.closeOtherTabs) {
        await this.closeNonWorkspaceTabs(workspace, switchOptions.preserveUserOpenedTabs!);
      }

      // 2. 打开工作区的所有网站
      const openResults = await this.openWorkspaceWebsites(workspace);
      if (!openResults.success) {
        console.error('Failed to open workspace websites:', openResults.error);
        // 继续执行，不要因为这个错误中断整个流程
      }
      
      // 3. 设置为活跃工作区
      await StorageManager.setActiveWorkspaceId(workspaceId);

      // 4. 更新工作区状态
      const workspacesResult = await StorageManager.getWorkspaces();
      if (workspacesResult.success) {
        const workspaces = workspacesResult.data!;
        workspaces.forEach(w => {
          w.isActive = w.id === workspaceId;
        });
        await StorageManager.saveWorkspaces(workspaces);
      }

      // 5. 如果需要，聚焦到第一个标签页
      if (switchOptions.focusFirstTab && workspace.websites.length > 0) {
        const firstWebsite = workspace.websites[0];
        const tabResult = await TabManager.findTabByUrl(firstWebsite.url);
        if (tabResult.success && tabResult.data) {
          await TabManager.activateTab(tabResult.data.id);
        }
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to switch workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 打开工作区的所有网站
   */
  private static async openWorkspaceWebsites(workspace: WorkSpace): Promise<OperationResult<void>> {
    try {
      console.log(`Opening workspace: ${workspace.name} with ${workspace.websites.length} websites`);

      for (const website of workspace.websites) {
        try {
          console.log(`Processing website: ${website.title} (${website.url})`);

          // 检查是否已经打开
          const existingTabResult = await TabManager.findTabByUrl(website.url);

          if (existingTabResult.success && existingTabResult.data) {
            // 标签页已存在，激活并固定
            const tab = existingTabResult.data;
            console.log(`Found existing tab for ${website.title}, activating and pinning`);

            await TabManager.activateTab(tab.id);

            // 总是固定工作区的标签页（除非明确设置为不固定）
            if (!tab.isPinned) {
              await TabManager.pinTab(tab.id, true);
              console.log(`Pinned tab for ${website.title}`);
            }
          } else {
            // 创建新标签页并固定
            console.log(`Creating new tab for ${website.title}`);
            const newTabResult = await TabManager.createTab(website.url, true, false);
            if (newTabResult.success) {
              console.log(`Created and pinned new tab for ${website.title}`);
            } else {
              console.error(`Failed to create tab for ${website.title}:`, newTabResult.error);
            }
          }
        } catch (error) {
          console.error(`Error processing website ${website.title}:`, error);
          // 继续处理其他网站，不要因为一个失败而停止
        }
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to open workspace websites',
          details: error,
        },
      };
    }
  }

  /**
   * 关闭非工作区相关的标签页
   */
  private static async closeNonWorkspaceTabs(
    workspace: WorkSpace,
    preserveUserOpenedTabs: boolean
  ): Promise<OperationResult<void>> {
    try {
      console.log(`Closing non-workspace tabs. Preserve user opened tabs: ${preserveUserOpenedTabs}`);

      const nonRelatedTabsResult = await TabManager.getNonWorkspaceRelatedTabs(workspace);
      if (!nonRelatedTabsResult.success) return nonRelatedTabsResult;

      const nonRelatedTabs = nonRelatedTabsResult.data!;
      console.log(`Found ${nonRelatedTabs.length} non-workspace related tabs`);
      
      // 过滤需要关闭的标签页
      const tabsToClose = nonRelatedTabs.filter(tab => {
        // 不关闭扩展页面和特殊页面
        if (tab.url.startsWith('chrome://') ||
            tab.url.startsWith('chrome-extension://') ||
            tab.url.startsWith('moz-extension://')) {
          return false;
        }

        // 如果设置了保留用户手动打开的标签页
        if (preserveUserOpenedTabs) {
          // 保留所有非固定的标签页（认为是用户手动打开的）
          // 只关闭固定的标签页（认为是之前工作区管理的）
          return tab.isPinned;
        }

        // 如果没有设置保留，则关闭所有非工作区相关的标签页
        return true;
      });

      // 关闭标签页
      if (tabsToClose.length > 0) {
        console.log(`Closing ${tabsToClose.length} tabs:`, tabsToClose.map(tab => ({ id: tab.id, url: tab.url, isPinned: tab.isPinned })));
        const tabIds = tabsToClose.map(tab => tab.id);
        await TabManager.closeTabs(tabIds);
        console.log(`Successfully closed ${tabsToClose.length} tabs`);
      } else {
        console.log('No tabs to close');
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to close non-workspace tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 获取当前活跃的工作区
   */
  static async getCurrentWorkspace(): Promise<OperationResult<WorkSpace | null>> {
    try {
      const activeIdResult = await StorageManager.getActiveWorkspaceId();
      if (!activeIdResult.success) return activeIdResult;

      const activeId = activeIdResult.data;
      if (!activeId) {
        return { success: true, data: null };
      }

      const workspaceResult = await StorageManager.getWorkspace(activeId);
      if (!workspaceResult.success) {
        // 如果工作区不存在，清除活跃状态
        await StorageManager.setActiveWorkspaceId(null);
        return { success: true, data: null };
      }

      return { success: true, data: workspaceResult.data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get current workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 智能检测当前应该激活的工作区
   */
  static async detectActiveWorkspace(): Promise<OperationResult<WorkSpace | null>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) return workspacesResult;

      const workspaces = workspacesResult.data!;
      if (workspaces.length === 0) {
        return { success: true, data: null };
      }

      const activeTabResult = await TabManager.getActiveTab();
      if (!activeTabResult.success) {
        return { success: true, data: null };
      }

      const activeTab = activeTabResult.data!;

      // 查找包含当前活跃标签页URL的工作区
      const matchingWorkspace = workspaces.find(workspace =>
        workspace.websites.some(website => 
          activeTab.url.startsWith(website.url)
        )
      );

      return { success: true, data: matchingWorkspace || null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to detect active workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 添加当前标签页到工作区
   */
  static async addCurrentTabToWorkspace(workspaceId: string): Promise<OperationResult<void>> {
    try {
      const activeTabResult = await TabManager.getActiveTab();
      if (!activeTabResult.success) return activeTabResult;

      const activeTab = activeTabResult.data!;

      // 添加到工作区
      const addResult = await WorkspaceManager.addWebsite(
        workspaceId, 
        activeTab.url, 
        {
          title: activeTab.title,
          favicon: activeTab.favicon,
          pinTab: activeTab.isPinned,
        }
      );

      return addResult;
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to add current tab to workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 快速切换到下一个工作区
   */
  static async switchToNextWorkspace(): Promise<OperationResult<void>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) return workspacesResult;

      const workspaces = workspacesResult.data!;
      if (workspaces.length === 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: 'No workspaces available',
          },
        };
      }

      const currentResult = await this.getCurrentWorkspace();
      if (!currentResult.success) return currentResult;

      const currentWorkspace = currentResult.data;
      let nextIndex = 0;

      if (currentWorkspace) {
        const currentIndex = workspaces.findIndex(w => w.id === currentWorkspace.id);
        nextIndex = (currentIndex + 1) % workspaces.length;
      }

      const nextWorkspace = workspaces[nextIndex];
      return await this.switchToWorkspace(nextWorkspace.id);
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to switch to next workspace',
          details: error,
        },
      };
    }
  }
}
