@tailwind base;
@tailwind components;
@tailwind utilities;

/* 基础样式重置 */
@layer base {
  * {
    box-sizing: border-box;
  }
  
  html, body {
    margin: 0;
    padding: 0;
    height: 100%;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  #root {
    height: 100vh;
    width: 100%;
    overflow: hidden;
  }

  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-track {
    background: #1e293b;
  }

  ::-webkit-scrollbar-thumb {
    background: #475569;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #64748b;
  }
}

/* 组件样式 */
@layer components {
  /* 工作区项目样式 */
  .workspace-item {
    @apply bg-slate-800 hover:bg-slate-700 rounded-lg p-3 cursor-pointer transition-all duration-200 border border-transparent hover:border-slate-600;
  }

  .workspace-item.active {
    @apply ring-2 ring-blue-500 bg-blue-900/20 border-blue-500/50;
  }

  /* 网站项目样式 */
  .website-item {
    @apply flex items-center gap-2 p-2 hover:bg-slate-600 rounded transition-all duration-150;
  }

  .website-item:hover .website-actions {
    @apply opacity-100;
  }

  .website-actions {
    @apply opacity-0 transition-opacity duration-150;
  }

  /* 按钮样式 */
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2;
  }

  .btn-secondary {
    @apply bg-slate-700 hover:bg-slate-600 text-slate-200 px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2;
  }

  .btn-danger {
    @apply bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2;
  }

  .btn-ghost {
    @apply hover:bg-slate-700 text-slate-300 hover:text-white px-3 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2;
  }

  /* 输入框样式 */
  .input-field {
    @apply bg-slate-700 border border-slate-600 text-white px-3 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200;
  }

  .input-field::placeholder {
    @apply text-slate-400;
  }

  /* 模态框样式 */
  .modal-overlay {
    @apply fixed inset-0 bg-black/50 flex items-center justify-center z-50;
  }

  .modal-content {
    @apply bg-slate-800 rounded-xl p-6 max-w-md w-full mx-4 border border-slate-700;
  }

  /* 下拉菜单样式 */
  .dropdown-menu {
    @apply absolute right-0 mt-2 w-48 bg-slate-800 border border-slate-700 rounded-lg shadow-lg z-10 py-1;
  }

  .dropdown-item {
    @apply px-4 py-2 text-sm text-slate-200 hover:bg-slate-700 cursor-pointer flex items-center gap-2;
  }

  /* 工具提示样式 */
  .tooltip {
    @apply absolute z-50 px-2 py-1 text-xs text-white bg-slate-900 rounded border border-slate-700 pointer-events-none;
  }

  /* 拖拽样式 */
  .dragging {
    @apply opacity-50 transform rotate-2;
  }

  .drop-zone {
    @apply border-2 border-dashed border-blue-500 bg-blue-500/10;
  }

  /* 加载动画 */
  .loading-spinner {
    @apply animate-spin rounded-full h-4 w-4 border-2 border-slate-400 border-t-transparent;
  }

  /* 渐变背景 */
  .gradient-bg {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }

  /* 卡片样式 */
  .card {
    @apply bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 rounded-xl p-4;
  }

  /* 分隔线 */
  .divider {
    @apply border-t border-slate-700 my-4;
  }

  /* 徽章样式 */
  .badge {
    @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply bg-blue-100 text-blue-800;
  }

  .badge-success {
    @apply bg-green-100 text-green-800;
  }

  .badge-warning {
    @apply bg-yellow-100 text-yellow-800;
  }

  .badge-danger {
    @apply bg-red-100 text-red-800;
  }
}

/* 工具类 */
@layer utilities {
  /* 文本截断 */
  .text-truncate {
    @apply truncate;
  }

  /* 隐藏滚动条 */
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* 玻璃效果 */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  /* 阴影效果 */
  .shadow-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  .shadow-glow-green {
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
  }

  .shadow-glow-red {
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
  }

  /* 动画类 */
  .animate-fade-in {
    animation: fadeIn 0.2s ease-in-out;
  }

  .animate-slide-in {
    animation: slideIn 0.3s ease-out;
  }

  .animate-bounce-subtle {
    animation: bounceSubtle 0.4s ease-in-out;
  }

  /* 状态指示器 */
  .status-dot {
    @apply w-2 h-2 rounded-full;
  }

  .status-online {
    @apply bg-green-500;
  }

  .status-offline {
    @apply bg-gray-500;
  }

  .status-busy {
    @apply bg-red-500;
  }
}

/* 动画定义 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-10px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes bounceSubtle {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}
