import React, { useState } from 'react';
import { X, Save, Download, Upload, Trash2 } from 'lucide-react';
import { Settings } from '@/types/workspace';
import { StorageManager } from '@/utils/storage';

interface SettingsPanelProps {
  settings: Settings;
  onClose: () => void;
  onUpdateSettings: (newSettings: Partial<Settings>) => void;
}

/**
 * 设置面板组件
 */
const SettingsPanel: React.FC<SettingsPanelProps> = ({
  settings,
  onClose,
  onUpdateSettings,
}) => {
  const [localSettings, setLocalSettings] = useState<Settings>(settings);
  const [hasChanges, setHasChanges] = useState(false);

  /**
   * 更新本地设置
   */
  const updateLocalSetting = <K extends keyof Settings>(
    key: K,
    value: Settings[K]
  ) => {
    const newSettings = { ...localSettings, [key]: value };
    setLocalSettings(newSettings);
    setHasChanges(true);
  };

  /**
   * 保存设置
   */
  const handleSave = () => {
    onUpdateSettings(localSettings);
    setHasChanges(false);
  };

  /**
   * 重置设置
   */
  const handleReset = () => {
    setLocalSettings(settings);
    setHasChanges(false);
  };

  /**
   * 导出数据
   */
  const handleExport = async () => {
    try {
      const result = await StorageManager.exportData();
      if (result.success) {
        const blob = new Blob([result.data!], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `workspace-pro-backup-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error('Failed to export data:', error);
    }
  };

  /**
   * 导入数据
   */
  const handleImport = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        try {
          const text = await file.text();
          const result = await StorageManager.importData(text);
          if (result.success) {
            window.location.reload();
          }
        } catch (error) {
          console.error('Failed to import data:', error);
        }
      }
    };
    input.click();
  };

  /**
   * 清除所有数据
   */
  const handleClearAll = async () => {
    if (confirm('确定要清除所有数据吗？此操作无法撤销。')) {
      try {
        await StorageManager.clearAll();
        window.location.reload();
      } catch (error) {
        console.error('Failed to clear data:', error);
      }
    }
  };

  /**
   * 处理ESC键关闭
   */
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  return (
    <div className="modal-overlay" onKeyDown={handleKeyDown}>
      <div className="modal-content animate-fade-in max-w-lg">
        {/* 头部 */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white">
            设置
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-slate-700 rounded-lg transition-colors duration-200"
          >
            <X className="w-5 h-5 text-slate-400" />
          </button>
        </div>

        {/* 设置内容 */}
        <div className="space-y-6 max-h-96 overflow-y-auto">
          {/* 标签页管理 */}
          <div>
            <h3 className="text-lg font-medium text-white mb-3">
              标签页管理
            </h3>
            <div className="space-y-3">
              <div className="p-3 bg-slate-700 rounded-lg">
                <div className="flex items-center gap-2 mb-1">
                  <span className="text-sm font-medium text-blue-400">ℹ️ 工作区隔离模式</span>
                </div>
                <p className="text-xs text-slate-300">
                  WorkSpace Pro 使用隐藏窗口技术实现真正的工作区隔离，保持所有标签页状态，提供瞬时切换体验。
                </p>
              </div>
              <label className="flex items-center justify-between">
                <span className="text-sm text-slate-200">
                  切换工作区时自动关闭其他标签页
                </span>
                <input
                  type="checkbox"
                  checked={localSettings.autoCloseOtherTabs}
                  onChange={(e) => updateLocalSetting('autoCloseOtherTabs', e.target.checked)}
                  className="w-4 h-4 text-blue-600 bg-slate-700 border-slate-600 rounded focus:ring-blue-500"
                />
              </label>
              <label className="flex items-center justify-between">
                <span className="text-sm text-slate-200">
                  保留用户手动打开的标签页
                </span>
                <input
                  type="checkbox"
                  checked={localSettings.preserveUserOpenedTabs}
                  onChange={(e) => updateLocalSetting('preserveUserOpenedTabs', e.target.checked)}
                  className="w-4 h-4 text-blue-600 bg-slate-700 border-slate-600 rounded focus:ring-blue-500"
                />
              </label>
            </div>
          </div>

          {/* 界面设置 */}
          <div>
            <h3 className="text-lg font-medium text-white mb-3">
              界面设置
            </h3>
            <div className="space-y-3">
              <label className="flex items-center justify-between">
                <span className="text-sm text-slate-200">
                  显示网站图标
                </span>
                <input
                  type="checkbox"
                  checked={localSettings.showFavicons}
                  onChange={(e) => updateLocalSetting('showFavicons', e.target.checked)}
                  className="w-4 h-4 text-blue-600 bg-slate-700 border-slate-600 rounded focus:ring-blue-500"
                />
              </label>
              <label className="flex items-center justify-between">
                <span className="text-sm text-slate-200">
                  删除前确认
                </span>
                <input
                  type="checkbox"
                  checked={localSettings.confirmBeforeDelete}
                  onChange={(e) => updateLocalSetting('confirmBeforeDelete', e.target.checked)}
                  className="w-4 h-4 text-blue-600 bg-slate-700 border-slate-600 rounded focus:ring-blue-500"
                />
              </label>
            </div>
          </div>

          {/* 高级设置 */}
          <div>
            <h3 className="text-lg font-medium text-white mb-3">
              高级设置
            </h3>
            <div className="space-y-3">
              <div>
                <label className="block text-sm text-slate-200 mb-1">
                  最大最近工作区数量
                </label>
                <input
                  type="number"
                  min="1"
                  max="10"
                  value={localSettings.maxRecentWorkspaces}
                  onChange={(e) => updateLocalSetting('maxRecentWorkspaces', parseInt(e.target.value))}
                  className="input-field w-20"
                />
              </div>
            </div>
          </div>

          {/* 数据管理 */}
          <div>
            <h3 className="text-lg font-medium text-white mb-3">
              数据管理
            </h3>
            <div className="space-y-2">
              <button
                onClick={handleExport}
                className="w-full btn-secondary justify-center"
              >
                <Download className="w-4 h-4" />
                导出数据
              </button>
              <button
                onClick={handleImport}
                className="w-full btn-secondary justify-center"
              >
                <Upload className="w-4 h-4" />
                导入数据
              </button>
              <button
                onClick={handleClearAll}
                className="w-full btn-danger justify-center"
              >
                <Trash2 className="w-4 h-4" />
                清除所有数据
              </button>
            </div>
          </div>
        </div>

        {/* 底部按钮 */}
        <div className="flex gap-3 justify-end mt-6 pt-4 border-t border-slate-700">
          <button
            onClick={handleReset}
            disabled={!hasChanges}
            className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            重置
          </button>
          <button
            onClick={onClose}
            className="btn-secondary"
          >
            关闭
          </button>
          <button
            onClick={handleSave}
            disabled={!hasChanges}
            className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Save className="w-4 h-4" />
            保存设置
          </button>
        </div>
      </div>
    </div>
  );
};

export default SettingsPanel;
