import React from 'react';
import { ExternalLink, X, Pin, Edit } from 'lucide-react';
import { Website } from '@/types/workspace';

interface WebsiteListProps {
  websites: Website[];
  onRemoveWebsite: (websiteId: string) => void;
  onEditWebsite: (website: Website) => void;
  onReorderWebsites?: (websiteIds: string[]) => void;
}

/**
 * 网站列表组件
 */
const WebsiteList: React.FC<WebsiteListProps> = ({
  websites,
  onRemoveWebsite,
  onEditWebsite,
  onReorderWebsites: _onReorderWebsites,
}) => {
  /**
   * 处理网站点击
   */
  const handleWebsiteClick = async (website: Website) => {
    try {
      // 检查是否已经打开
      const tabs = await chrome.tabs.query({ url: website.url });
      
      if (tabs.length > 0) {
        // 激活已存在的标签页
        await chrome.tabs.update(tabs[0].id!, { active: true });
      } else {
        // 创建新标签页
        await chrome.tabs.create({ 
          url: website.url,
          pinned: website.isPinned 
        });
      }
    } catch (error) {
      console.error('Failed to open website:', error);
    }
  };

  /**
   * 处理移除网站
   */
  const handleRemoveWebsite = (e: React.MouseEvent, websiteId: string) => {
    e.stopPropagation();
    onRemoveWebsite(websiteId);
  };

  /**
   * 处理编辑网站
   */
  const handleEditWebsite = (e: React.MouseEvent, website: Website) => {
    e.stopPropagation();
    onEditWebsite(website);
  };

  /**
   * 获取网站图标
   */
  const getWebsiteIcon = (website: Website) => {
    if (website.favicon && website.favicon !== '') {
      return (
        <img
          src={website.favicon}
          alt=""
          className="w-4 h-4 rounded"
          onError={(e) => {
            // 如果图标加载失败，显示默认图标
            (e.target as HTMLImageElement).style.display = 'none';
          }}
        />
      );
    }
    
    // 默认图标
    return (
      <div className="w-4 h-4 bg-slate-600 rounded flex items-center justify-center">
        <ExternalLink className="w-2.5 h-2.5 text-slate-400" />
      </div>
    );
  };

  /**
   * 格式化URL显示
   */
  const formatUrl = (url: string) => {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.replace('www.', '');
    } catch {
      return url;
    }
  };

  // 按order字段排序
  const sortedWebsites = [...websites].sort((a, b) => a.order - b.order);

  return (
    <div className="space-y-1">
      {sortedWebsites.map((website) => (
        <div
          key={website.id}
          className="website-item"
          onClick={() => handleWebsiteClick(website)}
        >
          {/* 网站图标 */}
          <div className="flex-shrink-0">
            {getWebsiteIcon(website)}
          </div>

          {/* 网站信息 */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-1">
              <span className="text-sm text-white truncate">
                {website.title}
              </span>
              {website.isPinned && (
                <Pin className="w-3 h-3 text-blue-400 flex-shrink-0" />
              )}
            </div>
            <p className="text-xs text-slate-400 truncate">
              {formatUrl(website.url)}
            </p>
          </div>

          {/* 操作按钮 */}
          <div className="website-actions flex items-center gap-1">
            <button
              onClick={(e) => {
                e.stopPropagation();
                window.open(website.url, '_blank');
              }}
              className="p-1 hover:bg-slate-500 rounded transition-colors duration-150"
              title="在新标签页中打开"
            >
              <ExternalLink className="w-3 h-3 text-slate-400" />
            </button>
            <button
              onClick={(e) => handleEditWebsite(e, website)}
              className="p-1 hover:bg-blue-600 rounded transition-colors duration-150"
              title="编辑网站"
            >
              <Edit className="w-3 h-3 text-slate-400 hover:text-white" />
            </button>
            <button
              onClick={(e) => handleRemoveWebsite(e, website.id)}
              className="p-1 hover:bg-red-600 rounded transition-colors duration-150"
              title="移除网站"
            >
              <X className="w-3 h-3 text-slate-400 hover:text-white" />
            </button>
          </div>
        </div>
      ))}
    </div>
  );
};

export default WebsiteList;
