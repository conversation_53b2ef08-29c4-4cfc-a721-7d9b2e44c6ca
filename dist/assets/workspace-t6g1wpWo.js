const STORAGE_KEYS = {
  WORKSPACES: "workspaces",
  SETTINGS: "settings",
  ACTIVE_WORKSPACE_ID: "activeWorkspaceId",
  LAST_ACTIVE_WORKSPACE_IDS: "lastActiveWorkspaceIds"
};
const DEFAULT_SETTINGS = {
  autoCloseOtherTabs: true,
  preserveUserOpenedTabs: false,
  defaultWorkspaceOnStartup: "",
  sidebarWidth: 320,
  theme: "dark",
  showFavicons: true,
  confirmBeforeDelete: true,
  maxRecentWorkspaces: 5
};
const WORKSPACE_COLORS = [
  "#3b82f6",
  // blue
  "#10b981",
  // emerald
  "#f59e0b",
  // amber
  "#ef4444",
  // red
  "#8b5cf6",
  // violet
  "#06b6d4",
  // cyan
  "#84cc16",
  // lime
  "#f97316",
  // orange
  "#ec4899",
  // pink
  "#6366f1"
  // indigo
];
const WORKSPACE_ICONS = [
  "🚀",
  "💼",
  "🔬",
  "🎨",
  "📊",
  "🛠️",
  "📚",
  "💡",
  "🎯",
  "⚡",
  "🌟",
  "🔥",
  "💎",
  "🎪",
  "🎭",
  "🎨",
  "🎵",
  "🎮",
  "🏆",
  "🎊",
  "📱",
  "💻",
  "🖥️",
  "⌨️",
  "🖱️",
  "🖨️",
  "📷",
  "📹",
  "🎥",
  "📺",
  "🔍",
  "🔎",
  "🔬",
  "🔭",
  "📡",
  "🛰️",
  "🚁",
  "✈️",
  "🚀",
  "🛸"
];
const WORKSPACE_TEMPLATES = [
  {
    id: "ai-tools",
    name: "AI工具集",
    description: "常用的AI工具和平台",
    icon: "🤖",
    color: "#3b82f6",
    category: "ai-tools",
    websites: [
      {
        url: "https://chat.openai.com",
        title: "ChatGPT",
        favicon: "https://chat.openai.com/favicon.ico",
        isPinned: true
      },
      {
        url: "https://claude.ai",
        title: "Claude",
        favicon: "https://claude.ai/favicon.ico",
        isPinned: true
      },
      {
        url: "https://gemini.google.com",
        title: "Gemini",
        favicon: "https://gemini.google.com/favicon.ico",
        isPinned: true
      }
    ]
  },
  {
    id: "development",
    name: "开发环境",
    description: "编程开发相关工具",
    icon: "💻",
    color: "#10b981",
    category: "development",
    websites: [
      {
        url: "https://github.com",
        title: "GitHub",
        favicon: "https://github.com/favicon.ico",
        isPinned: true
      },
      {
        url: "https://stackoverflow.com",
        title: "Stack Overflow",
        favicon: "https://stackoverflow.com/favicon.ico",
        isPinned: true
      },
      {
        url: "https://developer.mozilla.org",
        title: "MDN Web Docs",
        favicon: "https://developer.mozilla.org/favicon.ico",
        isPinned: true
      }
    ]
  },
  {
    id: "design",
    name: "设计工具",
    description: "设计和创意工具",
    icon: "🎨",
    color: "#f59e0b",
    category: "design",
    websites: [
      {
        url: "https://www.figma.com",
        title: "Figma",
        favicon: "https://www.figma.com/favicon.ico",
        isPinned: true
      },
      {
        url: "https://dribbble.com",
        title: "Dribbble",
        favicon: "https://dribbble.com/favicon.ico",
        isPinned: true
      },
      {
        url: "https://www.behance.net",
        title: "Behance",
        favicon: "https://www.behance.net/favicon.ico",
        isPinned: true
      }
    ]
  },
  {
    id: "research",
    name: "学术研究",
    description: "学术研究和论文查找",
    icon: "🔬",
    color: "#8b5cf6",
    category: "research",
    websites: [
      {
        url: "https://arxiv.org",
        title: "arXiv",
        favicon: "https://arxiv.org/favicon.ico",
        isPinned: true
      },
      {
        url: "https://scholar.google.com",
        title: "Google Scholar",
        favicon: "https://scholar.google.com/favicon.ico",
        isPinned: true
      },
      {
        url: "https://www.researchgate.net",
        title: "ResearchGate",
        favicon: "https://www.researchgate.net/favicon.ico",
        isPinned: true
      }
    ]
  },
  {
    id: "productivity",
    name: "生产力工具",
    description: "提高工作效率的工具",
    icon: "⚡",
    color: "#06b6d4",
    category: "productivity",
    websites: [
      {
        url: "https://notion.so",
        title: "Notion",
        favicon: "https://notion.so/favicon.ico",
        isPinned: true
      },
      {
        url: "https://trello.com",
        title: "Trello",
        favicon: "https://trello.com/favicon.ico",
        isPinned: true
      },
      {
        url: "https://calendar.google.com",
        title: "Google Calendar",
        favicon: "https://calendar.google.com/favicon.ico",
        isPinned: true
      }
    ]
  }
];
const ERROR_CODES = {
  WORKSPACE_NOT_FOUND: "WORKSPACE_NOT_FOUND",
  WEBSITE_NOT_FOUND: "WEBSITE_NOT_FOUND",
  STORAGE_ERROR: "STORAGE_ERROR",
  TAB_ERROR: "TAB_ERROR",
  PERMISSION_DENIED: "PERMISSION_DENIED",
  INVALID_URL: "INVALID_URL",
  DUPLICATE_WORKSPACE: "DUPLICATE_WORKSPACE",
  DUPLICATE_WEBSITE: "DUPLICATE_WEBSITE"
};
const COMMANDS = {
  SWITCH_WORKSPACE_1: "switch-workspace-1",
  SWITCH_WORKSPACE_2: "switch-workspace-2",
  SWITCH_WORKSPACE_3: "switch-workspace-3",
  TOGGLE_SIDEPANEL: "toggle-sidepanel"
};
const URL_REGEX = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/;
const DEFAULT_FAVICON = 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><path d="m9 12 2 2 4-4"/></svg>';

class StorageManager {
  /**
   * 获取所有存储数据
   */
  static async getAllData() {
    try {
      const result = await chrome.storage.local.get([
        STORAGE_KEYS.WORKSPACES,
        STORAGE_KEYS.SETTINGS,
        STORAGE_KEYS.ACTIVE_WORKSPACE_ID,
        STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS
      ]);
      const data = {
        workspaces: result[STORAGE_KEYS.WORKSPACES] || [],
        settings: { ...DEFAULT_SETTINGS, ...result[STORAGE_KEYS.SETTINGS] },
        activeWorkspaceId: result[STORAGE_KEYS.ACTIVE_WORKSPACE_ID] || null,
        lastActiveWorkspaceIds: result[STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS] || []
      };
      return { success: true, data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get storage data",
          details: error
        }
      };
    }
  }
  /**
   * 获取所有工作区
   */
  static async getWorkspaces() {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.WORKSPACES);
      const workspaces = result[STORAGE_KEYS.WORKSPACES] || [];
      workspaces.sort((a, b) => a.order - b.order);
      return { success: true, data: workspaces };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 保存工作区列表
   */
  static async saveWorkspaces(workspaces) {
    try {
      await chrome.storage.local.set({
        [STORAGE_KEYS.WORKSPACES]: workspaces
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 获取单个工作区
   */
  static async getWorkspace(id) {
    const result = await this.getWorkspaces();
    if (!result.success) {
      return {
        success: false,
        error: result.error
      };
    }
    const workspace = result.data.find((w) => w.id === id);
    if (!workspace) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.WORKSPACE_NOT_FOUND,
          message: `Workspace with id ${id} not found`
        }
      };
    }
    return { success: true, data: workspace };
  }
  /**
   * 获取设置
   */
  static async getSettings() {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.SETTINGS);
      const settings = { ...DEFAULT_SETTINGS, ...result[STORAGE_KEYS.SETTINGS] };
      return { success: true, data: settings };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get settings",
          details: error
        }
      };
    }
  }
  /**
   * 保存设置
   */
  static async saveSettings(settings) {
    try {
      const currentResult = await this.getSettings();
      if (!currentResult.success) {
        return {
          success: false,
          error: currentResult.error
        };
      }
      const updatedSettings = { ...currentResult.data, ...settings };
      await chrome.storage.local.set({
        [STORAGE_KEYS.SETTINGS]: updatedSettings
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save settings",
          details: error
        }
      };
    }
  }
  /**
   * 获取当前活跃工作区ID
   */
  static async getActiveWorkspaceId() {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.ACTIVE_WORKSPACE_ID);
      return { success: true, data: result[STORAGE_KEYS.ACTIVE_WORKSPACE_ID] || null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get active workspace ID",
          details: error
        }
      };
    }
  }
  /**
   * 设置当前活跃工作区ID
   */
  static async setActiveWorkspaceId(id) {
    try {
      await chrome.storage.local.set({
        [STORAGE_KEYS.ACTIVE_WORKSPACE_ID]: id
      });
      if (id) {
        await this.updateLastActiveWorkspaces(id);
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to set active workspace ID",
          details: error
        }
      };
    }
  }
  /**
   * 更新最近使用的工作区列表
   */
  static async updateLastActiveWorkspaces(workspaceId) {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS);
      let lastActiveIds = result[STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS] || [];
      lastActiveIds = lastActiveIds.filter((id) => id !== workspaceId);
      lastActiveIds.unshift(workspaceId);
      const settingsResult = await this.getSettings();
      const maxRecent = settingsResult.success ? settingsResult.data.maxRecentWorkspaces : 5;
      lastActiveIds = lastActiveIds.slice(0, maxRecent);
      await chrome.storage.local.set({
        [STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS]: lastActiveIds
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to update last active workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 清除所有数据
   */
  static async clearAll() {
    try {
      await chrome.storage.local.clear();
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to clear storage",
          details: error
        }
      };
    }
  }
  /**
   * 监听存储变化
   */
  static onChanged(callback) {
    chrome.storage.onChanged.addListener((changes, areaName) => {
      if (areaName === "local") {
        callback(changes);
      }
    });
  }
  /**
   * 导出数据
   */
  static async exportData() {
    try {
      const dataResult = await this.getAllData();
      if (!dataResult.success) {
        return {
          success: false,
          error: dataResult.error
        };
      }
      const exportData = {
        version: "1.0.0",
        exportedAt: Date.now(),
        workspaces: dataResult.data.workspaces,
        settings: dataResult.data.settings
      };
      return { success: true, data: JSON.stringify(exportData, null, 2) };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to export data",
          details: error
        }
      };
    }
  }
  /**
   * 导入数据
   */
  static async importData(jsonData) {
    try {
      const importData = JSON.parse(jsonData);
      if (importData.workspaces) {
        await this.saveWorkspaces(importData.workspaces);
      }
      if (importData.settings) {
        await this.saveSettings(importData.settings);
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to import data",
          details: error
        }
      };
    }
  }
}

class TabManager {
  /**
   * 获取所有标签页信息
   */
  static async getAllTabs() {
    try {
      const tabs = await chrome.tabs.query({});
      const tabInfos = tabs.map((tab) => ({
        id: tab.id,
        url: tab.url || "",
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId
      }));
      return { success: true, data: tabInfos };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get tabs",
          details: error
        }
      };
    }
  }
  /**
   * 获取当前活跃标签页
   */
  static async getActiveTab() {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length === 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: "No active tab found"
          }
        };
      }
      const tab = tabs[0];
      const tabInfo = {
        id: tab.id,
        url: tab.url || "",
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId
      };
      return { success: true, data: tabInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get active tab",
          details: error
        }
      };
    }
  }
  /**
   * 检查URL是否已在标签页中打开
   */
  static async findTabByUrl(url) {
    try {
      let tabs = await chrome.tabs.query({ url });
      if (tabs.length === 0) {
        try {
          const targetDomain = new URL(url).hostname;
          const allTabs = await chrome.tabs.query({});
          tabs = allTabs.filter((tab2) => {
            if (!tab2.url) return false;
            try {
              const tabDomain = new URL(tab2.url).hostname;
              return tabDomain === targetDomain;
            } catch {
              return false;
            }
          });
        } catch {
          return { success: true, data: null };
        }
      }
      if (tabs.length === 0) {
        return { success: true, data: null };
      }
      const tab = tabs[0];
      const tabInfo = {
        id: tab.id,
        url: tab.url || "",
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId
      };
      return { success: true, data: tabInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to find tab by URL",
          details: error
        }
      };
    }
  }
  /**
   * 创建新标签页
   */
  static async createTab(url, pinned = false, active = true) {
    try {
      const tab = await chrome.tabs.create({
        url,
        pinned,
        active
      });
      const tabInfo = {
        id: tab.id,
        url: tab.url || "",
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId
      };
      return { success: true, data: tabInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to create tab",
          details: error
        }
      };
    }
  }
  /**
   * 激活标签页（带重试机制）
   */
  static async activateTab(tabId, retries = 3) {
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        console.log(`Activating tab ${tabId} (attempt ${attempt}/${retries})`);
        await chrome.tabs.update(tabId, { active: true });
        console.log(`Successfully activated tab ${tabId}`);
        return { success: true };
      } catch (error) {
        const errorMessage = error?.message || "";
        if (errorMessage.includes("user may be dragging") || errorMessage.includes("Tabs cannot be edited right now")) {
          console.warn(`Tab activation blocked (attempt ${attempt}/${retries}): ${errorMessage}`);
          if (attempt < retries) {
            await new Promise((resolve) => setTimeout(resolve, 200 * attempt));
            continue;
          }
        }
        console.error(`Failed to activate tab ${tabId} after ${attempt} attempts:`, error);
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: "Failed to activate tab",
            details: error
          }
        };
      }
    }
    return {
      success: false,
      error: {
        code: ERROR_CODES.TAB_ERROR,
        message: "Failed to activate tab after all retries"
      }
    };
  }
  /**
   * 固定/取消固定标签页（带重试机制）
   */
  static async pinTab(tabId, pinned, retries = 3) {
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        console.log(`${pinned ? "Pinning" : "Unpinning"} tab ${tabId} (attempt ${attempt}/${retries})`);
        await chrome.tabs.update(tabId, { pinned });
        console.log(`Successfully ${pinned ? "pinned" : "unpinned"} tab ${tabId}`);
        return { success: true };
      } catch (error) {
        const errorMessage = error?.message || "";
        if (errorMessage.includes("user may be dragging") || errorMessage.includes("Tabs cannot be edited right now")) {
          console.warn(`Tab pin operation blocked (attempt ${attempt}/${retries}): ${errorMessage}`);
          if (attempt < retries) {
            await new Promise((resolve) => setTimeout(resolve, 200 * attempt));
            continue;
          }
        }
        console.error(`Failed to ${pinned ? "pin" : "unpin"} tab ${tabId} after ${attempt} attempts:`, error);
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: "Failed to pin/unpin tab",
            details: error
          }
        };
      }
    }
    return {
      success: false,
      error: {
        code: ERROR_CODES.TAB_ERROR,
        message: "Failed to pin/unpin tab after all retries"
      }
    };
  }
  /**
   * 关闭标签页
   */
  static async closeTab(tabId) {
    try {
      await chrome.tabs.remove(tabId);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to close tab",
          details: error
        }
      };
    }
  }
  /**
   * 关闭多个标签页
   */
  static async closeTabs(tabIds) {
    try {
      await chrome.tabs.remove(tabIds);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to close tabs",
          details: error
        }
      };
    }
  }
  /**
   * 获取工作区相关的标签页
   */
  static async getWorkspaceRelatedTabs(workspace) {
    try {
      const allTabsResult = await this.getAllTabs();
      if (!allTabsResult.success) return allTabsResult;
      const allTabs = allTabsResult.data;
      const workspaceUrls = workspace.websites.map((w) => w.url);
      const relatedTabs = allTabs.filter(
        (tab) => workspaceUrls.some((url) => tab.url.startsWith(url))
      );
      return { success: true, data: relatedTabs };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get workspace related tabs",
          details: error
        }
      };
    }
  }
  /**
   * 获取非工作区相关的标签页
   */
  static async getNonWorkspaceRelatedTabs(workspace) {
    try {
      const allTabsResult = await this.getAllTabs();
      if (!allTabsResult.success) return allTabsResult;
      const allTabs = allTabsResult.data;
      const workspaceUrls = workspace.websites.map((w) => w.url);
      const nonRelatedTabs = allTabs.filter(
        (tab) => !workspaceUrls.some((url) => tab.url.startsWith(url))
      );
      return { success: true, data: nonRelatedTabs };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get non-workspace related tabs",
          details: error
        }
      };
    }
  }
  /**
   * 检查标签页是否为用户手动打开的
   */
  static async isUserOpenedTab(tabId) {
    try {
      const tab = await chrome.tabs.get(tabId);
      return !tab.pinned;
    } catch {
      return false;
    }
  }
}

const tabs = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  TabManager
}, Symbol.toStringTag, { value: 'Module' }));

const scriptRel = 'modulepreload';const assetsURL = function(dep) { return "/"+dep };const seen = {};const __vitePreload = function preload(baseModule, deps, importerUrl) {
  let promise = Promise.resolve();
  if (true && deps && deps.length > 0) {
    document.getElementsByTagName("link");
    const cspNonceMeta = document.querySelector(
      "meta[property=csp-nonce]"
    );
    const cspNonce = cspNonceMeta?.nonce || cspNonceMeta?.getAttribute("nonce");
    promise = Promise.allSettled(
      deps.map((dep) => {
        dep = assetsURL(dep);
        if (dep in seen) return;
        seen[dep] = true;
        const isCss = dep.endsWith(".css");
        const cssSelector = isCss ? '[rel="stylesheet"]' : "";
        if (document.querySelector(`link[href="${dep}"]${cssSelector}`)) {
          return;
        }
        const link = document.createElement("link");
        link.rel = isCss ? "stylesheet" : scriptRel;
        if (!isCss) {
          link.as = "script";
        }
        link.crossOrigin = "";
        link.href = dep;
        if (cspNonce) {
          link.setAttribute("nonce", cspNonce);
        }
        document.head.appendChild(link);
        if (isCss) {
          return new Promise((res, rej) => {
            link.addEventListener("load", res);
            link.addEventListener(
              "error",
              () => rej(new Error(`Unable to preload CSS for ${dep}`))
            );
          });
        }
      })
    );
  }
  function handlePreloadError(err) {
    const e = new Event("vite:preloadError", {
      cancelable: true
    });
    e.payload = err;
    window.dispatchEvent(e);
    if (!e.defaultPrevented) {
      throw err;
    }
  }
  return promise.then((res) => {
    for (const item of res || []) {
      if (item.status !== "rejected") continue;
      handlePreloadError(item.reason);
    }
    return baseModule().catch(handlePreloadError);
  });
};

class WindowManager {
  // 隐藏窗口的位置配置
  static HIDDEN_WINDOW_CONFIG = {
    left: -3e3,
    top: -3e3,
    width: 800,
    height: 600,
    type: "normal",
    focused: false
  };
  /**
   * 获取当前主窗口
   */
  static async getCurrentWindow() {
    try {
      const window = await chrome.windows.getCurrent();
      const windowInfo = {
        id: window.id,
        focused: window.focused,
        incognito: window.incognito,
        state: window.state,
        type: window.type,
        left: window.left,
        top: window.top,
        width: window.width,
        height: window.height
      };
      return { success: true, data: windowInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get current window",
          details: error
        }
      };
    }
  }
  /**
   * 创建隐藏窗口
   */
  static async createHiddenWindow() {
    try {
      console.log("🪟 创建隐藏窗口...");
      const window = await chrome.windows.create({
        ...this.HIDDEN_WINDOW_CONFIG,
        url: "chrome://newtab/"
        // 创建一个新标签页
      });
      if (!window.id) {
        throw new Error("创建窗口失败 - 未返回窗口ID");
      }
      console.log(`✅ 隐藏窗口创建成功，ID: ${window.id}`);
      try {
        await chrome.windows.update(window.id, {
          state: "minimized",
          focused: false
        });
        console.log(`✅ 隐藏窗口已最小化: ${window.id}`);
      } catch (minimizeError) {
        console.warn(`⚠️ 无法最小化隐藏窗口 ${window.id}，但窗口已创建:`, minimizeError);
      }
      return { success: true, data: window.id };
    } catch (error) {
      console.error("❌ 创建隐藏窗口失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to create hidden window",
          details: error
        }
      };
    }
  }
  /**
   * 检查窗口是否存在
   */
  static async windowExists(windowId) {
    try {
      await chrome.windows.get(windowId);
      return true;
    } catch {
      return false;
    }
  }
  /**
   * 获取或创建隐藏窗口
   */
  static async getOrCreateHiddenWindow(existingWindowId) {
    try {
      if (existingWindowId && await this.windowExists(existingWindowId)) {
        console.log(`🔄 使用现有隐藏窗口: ${existingWindowId}`);
        return { success: true, data: existingWindowId };
      }
      console.log("🆕 创建新的隐藏窗口");
      return await this.createHiddenWindow();
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get or create hidden window",
          details: error
        }
      };
    }
  }
  /**
   * 关闭隐藏窗口
   */
  static async closeHiddenWindow(windowId) {
    try {
      if (await this.windowExists(windowId)) {
        console.log(`Closing hidden window: ${windowId}`);
        await chrome.windows.remove(windowId);
        console.log(`Hidden window ${windowId} closed successfully`);
      }
      return { success: true };
    } catch (error) {
      console.error(`Failed to close hidden window ${windowId}:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to close hidden window",
          details: error
        }
      };
    }
  }
  /**
   * 获取窗口中的所有标签页
   */
  static async getWindowTabs(windowId) {
    try {
      const tabs = await chrome.tabs.query({ windowId });
      const tabInfos = tabs.map((tab) => ({
        id: tab.id,
        url: tab.url || "",
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId
      }));
      return { success: true, data: tabInfos };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get window tabs",
          details: error
        }
      };
    }
  }
  /**
   * 移动标签页到指定窗口（带重试和错误处理）
   */
  static async moveTabsToWindow(tabIds, targetWindowId, options = {}) {
    try {
      if (tabIds.length === 0) {
        return { success: true };
      }
      console.log(`🚀 移动 ${tabIds.length} 个标签页到窗口 ${targetWindowId}`);
      await new Promise((resolve) => setTimeout(resolve, 100));
      let movedTabs;
      let retries = 3;
      for (let attempt = 1; attempt <= retries; attempt++) {
        try {
          movedTabs = await chrome.tabs.move(tabIds, {
            windowId: targetWindowId,
            index: -1
            // 移动到窗口末尾
          });
          break;
        } catch (error) {
          const errorMessage = error?.message || "";
          if ((errorMessage.includes("user may be dragging") || errorMessage.includes("Tabs cannot be edited right now")) && attempt < retries) {
            console.warn(`⚠️ 标签页移动被阻止 (尝试 ${attempt}/${retries})，重试中...`);
            await new Promise((resolve) => setTimeout(resolve, 300 * attempt));
            continue;
          }
          throw error;
        }
      }
      console.log(`✅ 成功移动 ${Array.isArray(movedTabs) ? movedTabs.length : 1} 个标签页`);
      if (options.preservePinned) {
        for (const tabId of tabIds) {
          try {
            const tab = await chrome.tabs.get(tabId);
            if (tab.pinned) {
              const { TabManager } = await __vitePreload(async () => { const { TabManager } = await Promise.resolve().then(() => tabs);return { TabManager }},true?void 0:void 0);
              await TabManager.pinTab(tabId, true);
            }
          } catch (error) {
            console.warn(`Failed to preserve pinned state for tab ${tabId}:`, error);
          }
        }
      }
      return { success: true };
    } catch (error) {
      console.error("Failed to move tabs to window:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to move tabs to window",
          details: error
        }
      };
    }
  }
  /**
   * 确保窗口至少有一个标签页
   */
  static async ensureWindowHasTab(windowId) {
    try {
      const tabsResult = await this.getWindowTabs(windowId);
      if (!tabsResult.success) return tabsResult;
      const tabs = tabsResult.data;
      console.log(`🔍 窗口 ${windowId} 当前有 ${tabs.length} 个标签页`);
      if (tabs.length === 0) {
        console.log(`🆕 为空窗口 ${windowId} 创建新标签页`);
        const newTab = await chrome.tabs.create({
          windowId,
          url: "chrome://newtab/",
          active: true
        });
        console.log(`✅ 在窗口 ${windowId} 中创建了新标签页 ${newTab.id}`);
      } else {
        console.log(`✅ 窗口 ${windowId} 已有标签页，无需操作`);
      }
      return { success: true };
    } catch (error) {
      console.error(`Failed to ensure window ${windowId} has tab:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to ensure window has tab",
          details: error
        }
      };
    }
  }
  /**
   * 清理所有隐藏窗口
   */
  static async cleanupAllHiddenWindows() {
    try {
      console.log("Cleaning up all hidden windows...");
      const windows = await chrome.windows.getAll();
      const hiddenWindows = windows.filter(
        (window) => window.left === this.HIDDEN_WINDOW_CONFIG.left && window.top === this.HIDDEN_WINDOW_CONFIG.top
      );
      for (const window of hiddenWindows) {
        if (window.id) {
          await this.closeHiddenWindow(window.id);
        }
      }
      console.log(`Cleaned up ${hiddenWindows.length} hidden windows`);
      return { success: true };
    } catch (error) {
      console.error("Failed to cleanup hidden windows:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to cleanup hidden windows",
          details: error
        }
      };
    }
  }
}

class WorkspaceSwitcher {
  /**
   * 切换到指定工作区 - 使用隐藏窗口隔离机制
   */
  static async switchToWorkspace(workspaceId, options = {}) {
    try {
      console.log(`🔄 开始切换到工作区: ${workspaceId}`);
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) return workspaceResult;
      const targetWorkspace = workspaceResult.data;
      console.log(`📋 目标工作区: ${targetWorkspace.name}`);
      const currentWorkspaceResult = await this.getCurrentWorkspace();
      const currentWorkspace = currentWorkspaceResult.success ? currentWorkspaceResult.data : null;
      console.log(`📋 当前工作区: ${currentWorkspace?.name || "无"}`);
      if (currentWorkspace && currentWorkspace.id === workspaceId) {
        console.log("⚠️ 已在目标工作区，无需切换");
        return { success: true };
      }
      const settingsResult = await StorageManager.getSettings();
      if (!settingsResult.success) return settingsResult;
      const settings = settingsResult.data;
      const switchOptions = {
        closeOtherTabs: options.closeOtherTabs ?? settings.autoCloseOtherTabs,
        preserveUserOpenedTabs: options.preserveUserOpenedTabs ?? settings.preserveUserOpenedTabs,
        focusFirstTab: options.focusFirstTab ?? true
      };
      const switchResult = await this.switchWithHiddenWindows(currentWorkspace, targetWorkspace, switchOptions);
      if (!switchResult.success) return switchResult;
      await StorageManager.setActiveWorkspaceId(workspaceId);
      const workspacesResult = await StorageManager.getWorkspaces();
      if (workspacesResult.success) {
        const workspaces = workspacesResult.data;
        workspaces.forEach((w) => {
          w.isActive = w.id === workspaceId;
        });
        await StorageManager.saveWorkspaces(workspaces);
      }
      console.log(`✅ 工作区切换成功完成: ${targetWorkspace.name}`);
      return { success: true };
    } catch (error) {
      console.error("❌ 工作区切换失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to switch workspace",
          details: error
        }
      };
    }
  }
  /**
   * 使用隐藏窗口模式切换工作区
   */
  static async switchWithHiddenWindows(currentWorkspace, targetWorkspace, options) {
    try {
      console.log(`🔄 === 开始隐藏窗口模式切换: ${currentWorkspace?.name || "无"} → ${targetWorkspace.name} ===`);
      const currentWindowResult = await WindowManager.getCurrentWindow();
      if (!currentWindowResult.success) return currentWindowResult;
      const currentWindow = currentWindowResult.data;
      console.log(`🪟 主窗口ID: ${currentWindow.id}`);
      const beforeTabsResult = await WindowManager.getWindowTabs(currentWindow.id);
      if (beforeTabsResult.success) {
        const beforeTabs = beforeTabsResult.data;
        console.log(
          `📊 切换前状态 - 主窗口有 ${beforeTabs.length} 个标签页:`,
          beforeTabs.map((tab) => ({ id: tab.id, url: tab.url, title: tab.title }))
        );
      }
      if (currentWorkspace) {
        console.log(`📤 步骤1: 将当前工作区 (${currentWorkspace.name}) 的标签页移动到隐藏窗口`);
        const moveToHiddenResult = await this.moveCurrentTabsToHiddenWindow(currentWorkspace, currentWindow.id);
        if (!moveToHiddenResult.success) {
          console.error("❌ 移动当前标签页到隐藏窗口失败:", moveToHiddenResult.error);
        }
      } else {
        console.log("📤 步骤1: 无当前工作区，跳过标签页移动到隐藏窗口");
      }
      console.log(`🔍 步骤2: 验证主窗口已清空`);
      const midTabsResult = await WindowManager.getWindowTabs(currentWindow.id);
      if (midTabsResult.success) {
        const midTabs = midTabsResult.data;
        console.log(
          `📊 移动后验证 - 主窗口有 ${midTabs.length} 个标签页:`,
          midTabs.map((tab) => ({ id: tab.id, url: tab.url, title: tab.title }))
        );
        if (midTabs.length > 1) {
          console.warn(`⚠️ 警告: 主窗口仍有 ${midTabs.length} 个标签页，隔离可能不完整`);
        }
      }
      console.log(`📥 步骤3: 从目标工作区 (${targetWorkspace.name}) 的隐藏窗口移动标签页`);
      const moveFromHiddenResult = await this.moveTabsFromHiddenWindow(targetWorkspace, currentWindow.id);
      if (!moveFromHiddenResult.success) {
        console.error("❌ 从隐藏窗口移动标签页失败:", moveFromHiddenResult.error);
        console.log("🔄 回退方案: 创建工作区网站");
        await this.openWorkspaceWebsites(targetWorkspace);
      }
      console.log("🔍 步骤4: 确保主窗口至少有一个标签页");
      await WindowManager.ensureWindowHasTab(currentWindow.id);
      const afterTabsResult = await WindowManager.getWindowTabs(currentWindow.id);
      if (afterTabsResult.success) {
        const afterTabs = afterTabsResult.data;
        console.log(
          `📊 最终状态 - 主窗口有 ${afterTabs.length} 个标签页:`,
          afterTabs.map((tab) => ({ id: tab.id, url: tab.url, title: tab.title }))
        );
      }
      if (options.focusFirstTab) {
        console.log("🎯 步骤5: 聚焦第一个标签页");
        const tabsResult = await WindowManager.getWindowTabs(currentWindow.id);
        if (tabsResult.success && tabsResult.data.length > 0) {
          const firstTab = tabsResult.data[0];
          const activateResult = await TabManager.activateTab(firstTab.id);
          if (!activateResult.success) {
            console.warn("⚠️ 激活第一个标签页失败，但继续执行:", activateResult.error);
          }
        }
      }
      console.log(`✅ === 工作区切换完成: ${targetWorkspace.name} ===`);
      return { success: true };
    } catch (error) {
      console.error("❌ 隐藏窗口切换错误:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to switch with hidden windows",
          details: error
        }
      };
    }
  }
  /**
   * 打开工作区的所有网站
   */
  static async openWorkspaceWebsites(workspace) {
    try {
      console.log(`🌐 打开工作区网站: ${workspace.name}，包含 ${workspace.websites.length} 个网站`);
      for (const website of workspace.websites) {
        try {
          console.log(`🔗 处理网站: ${website.title} (${website.url})`);
          const existingTabResult = await TabManager.findTabByUrl(website.url);
          if (existingTabResult.success && existingTabResult.data) {
            const tab = existingTabResult.data;
            console.log(`✅ 发现现有标签页: ${website.title}，激活中`);
            await TabManager.activateTab(tab.id);
            console.log(`✅ 已激活标签页: ${website.title}`);
          } else {
            console.log(`🆕 为 ${website.title} 创建新标签页`);
            const newTabResult = await TabManager.createTab(website.url, false, false);
            if (newTabResult.success) {
              console.log(`✅ 成功创建新标签页: ${website.title}`);
            } else {
              console.error(`❌ 创建标签页失败: ${website.title}:`, newTabResult.error);
            }
          }
        } catch (error) {
          console.error(`Error processing website ${website.title}:`, error);
        }
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to open workspace websites",
          details: error
        }
      };
    }
  }
  /**
   * 将当前窗口的标签页移动到工作区的隐藏窗口
   */
  static async moveCurrentTabsToHiddenWindow(workspace, currentWindowId) {
    try {
      console.log(`📤 开始移动当前标签页到隐藏窗口 - 工作区: ${workspace.name}`);
      const tabsResult = await WindowManager.getWindowTabs(currentWindowId);
      if (!tabsResult.success) return tabsResult;
      const tabs = tabsResult.data;
      console.log(`📋 当前窗口发现 ${tabs.length} 个标签页:`, tabs.map((tab) => ({ id: tab.id, url: tab.url, title: tab.title })));
      if (tabs.length === 0) {
        console.log("⚠️ 没有标签页需要移动");
        return { success: true };
      }
      console.log(`🔍 获取或创建隐藏窗口 - 当前隐藏窗口ID: ${workspace.hiddenWindowId || "无"}`);
      const hiddenWindowResult = await WindowManager.getOrCreateHiddenWindow(workspace.hiddenWindowId);
      if (!hiddenWindowResult.success) return hiddenWindowResult;
      const hiddenWindowId = hiddenWindowResult.data;
      console.log(`🪟 隐藏窗口ID: ${hiddenWindowId}`);
      if (workspace.hiddenWindowId !== hiddenWindowId) {
        console.log(`📝 更新工作区隐藏窗口ID: ${workspace.hiddenWindowId} → ${hiddenWindowId}`);
        workspace.hiddenWindowId = hiddenWindowId;
        await this.updateWorkspaceHiddenWindowId(workspace.id, hiddenWindowId);
      }
      const tabIds = tabs.map((tab) => tab.id);
      console.log(`🚀 开始移动所有 ${tabIds.length} 个标签页到隐藏窗口 ${hiddenWindowId}`);
      console.log(`📋 移动的标签页ID列表:`, tabIds);
      const moveResult = await WindowManager.moveTabsToWindow(tabIds, hiddenWindowId, {
        preserveOrder: true,
        preservePinned: true
      });
      if (!moveResult.success) {
        console.error("❌ 移动标签页到隐藏窗口失败:", moveResult.error);
        return moveResult;
      }
      console.log(`🔍 确保主窗口 ${currentWindowId} 有新标签页`);
      await WindowManager.ensureWindowHasTab(currentWindowId);
      console.log(`✅ 成功移动所有 ${tabIds.length} 个标签页到隐藏窗口 ${hiddenWindowId}`);
      return { success: true };
    } catch (error) {
      console.error("Failed to move current tabs to hidden window:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to move current tabs to hidden window",
          details: error
        }
      };
    }
  }
  /**
   * 从工作区的隐藏窗口移动标签页到主窗口
   */
  static async moveTabsFromHiddenWindow(workspace, targetWindowId) {
    try {
      console.log(`📥 开始从隐藏窗口移动标签页 - 工作区: ${workspace.name}`);
      if (!workspace.hiddenWindowId) {
        console.log("⚠️ 未找到隐藏窗口，创建工作区网站");
        return await this.openWorkspaceWebsites(workspace);
      }
      console.log(`🔍 检查隐藏窗口是否存在 - 隐藏窗口ID: ${workspace.hiddenWindowId}`);
      const windowExists = await WindowManager.windowExists(workspace.hiddenWindowId);
      if (!windowExists) {
        console.log("⚠️ 隐藏窗口不再存在，创建工作区网站");
        workspace.hiddenWindowId = void 0;
        await this.updateWorkspaceHiddenWindowId(workspace.id, void 0);
        return await this.openWorkspaceWebsites(workspace);
      }
      console.log(`📋 获取隐藏窗口 ${workspace.hiddenWindowId} 的标签页`);
      const tabsResult = await WindowManager.getWindowTabs(workspace.hiddenWindowId);
      if (!tabsResult.success) return tabsResult;
      const tabs = tabsResult.data;
      console.log(
        `📋 隐藏窗口 ${workspace.hiddenWindowId} 中发现 ${tabs.length} 个标签页:`,
        tabs.map((tab) => ({ id: tab.id, url: tab.url, title: tab.title }))
      );
      if (tabs.length === 0) {
        console.log("⚠️ 隐藏窗口中没有标签页，创建工作区网站");
        return await this.openWorkspaceWebsites(workspace);
      }
      const currentTabsResult = await WindowManager.getWindowTabs(targetWindowId);
      if (currentTabsResult.success) {
        const currentTabs = currentTabsResult.data;
        console.log(
          `📊 目标窗口 ${targetWindowId} 当前有 ${currentTabs.length} 个标签页:`,
          currentTabs.map((tab) => ({ id: tab.id, url: tab.url, title: tab.title }))
        );
      }
      const tabIds = tabs.map((tab) => tab.id);
      console.log(`🚀 开始移动 ${tabIds.length} 个标签页从隐藏窗口 ${workspace.hiddenWindowId} 到主窗口 ${targetWindowId}`);
      console.log(`📋 移动的标签页ID列表:`, tabIds);
      const moveResult = await WindowManager.moveTabsToWindow(tabIds, targetWindowId, {
        preserveOrder: true,
        preservePinned: true
      });
      if (!moveResult.success) {
        console.error("❌ 从隐藏窗口移动标签页到主窗口失败:", moveResult.error);
        return moveResult;
      }
      const finalTabsResult = await WindowManager.getWindowTabs(targetWindowId);
      if (finalTabsResult.success) {
        const finalTabs = finalTabsResult.data;
        console.log(
          `📊 移动后状态 - 主窗口 ${targetWindowId} 有 ${finalTabs.length} 个标签页:`,
          finalTabs.map((tab) => ({ id: tab.id, url: tab.url, title: tab.title }))
        );
      }
      console.log(`✅ 成功移动 ${tabs.length} 个标签页从隐藏窗口到主窗口`);
      return { success: true };
    } catch (error) {
      console.error("Failed to move tabs from hidden window:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to move tabs from hidden window",
          details: error
        }
      };
    }
  }
  /**
   * 更新工作区的隐藏窗口ID
   */
  static async updateWorkspaceHiddenWindowId(workspaceId, hiddenWindowId) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) return workspacesResult;
      const workspaces = workspacesResult.data;
      const workspace = workspaces.find((w) => w.id === workspaceId);
      if (workspace) {
        workspace.hiddenWindowId = hiddenWindowId;
        workspace.updatedAt = Date.now();
        await StorageManager.saveWorkspaces(workspaces);
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to update workspace hidden window ID",
          details: error
        }
      };
    }
  }
  /**
   * 关闭非工作区相关的标签页
   */
  static async closeNonWorkspaceTabs(workspace, preserveUserOpenedTabs) {
    try {
      console.log(`Closing non-workspace tabs. Preserve user opened tabs: ${preserveUserOpenedTabs}`);
      const nonRelatedTabsResult = await TabManager.getNonWorkspaceRelatedTabs(workspace);
      if (!nonRelatedTabsResult.success) return nonRelatedTabsResult;
      const nonRelatedTabs = nonRelatedTabsResult.data;
      console.log(`Found ${nonRelatedTabs.length} non-workspace related tabs`);
      const tabsToClose = nonRelatedTabs.filter((tab) => {
        if (tab.url.startsWith("chrome://") || tab.url.startsWith("chrome-extension://") || tab.url.startsWith("moz-extension://")) {
          return false;
        }
        if (preserveUserOpenedTabs) {
          return tab.isPinned;
        }
        return true;
      });
      if (tabsToClose.length > 0) {
        console.log(`Closing ${tabsToClose.length} tabs:`, tabsToClose.map((tab) => ({ id: tab.id, url: tab.url, isPinned: tab.isPinned })));
        const tabIds = tabsToClose.map((tab) => tab.id);
        await TabManager.closeTabs(tabIds);
        console.log(`Successfully closed ${tabsToClose.length} tabs`);
      } else {
        console.log("No tabs to close");
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to close non-workspace tabs",
          details: error
        }
      };
    }
  }
  /**
   * 获取当前活跃的工作区
   */
  static async getCurrentWorkspace() {
    try {
      const activeIdResult = await StorageManager.getActiveWorkspaceId();
      if (!activeIdResult.success) return activeIdResult;
      const activeId = activeIdResult.data;
      if (!activeId) {
        return { success: true, data: null };
      }
      const workspaceResult = await StorageManager.getWorkspace(activeId);
      if (!workspaceResult.success) {
        await StorageManager.setActiveWorkspaceId(null);
        return { success: true, data: null };
      }
      return { success: true, data: workspaceResult.data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get current workspace",
          details: error
        }
      };
    }
  }
  /**
   * 智能检测当前应该激活的工作区
   */
  static async detectActiveWorkspace() {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) return workspacesResult;
      const workspaces = workspacesResult.data;
      if (workspaces.length === 0) {
        return { success: true, data: null };
      }
      const activeTabResult = await TabManager.getActiveTab();
      if (!activeTabResult.success) {
        return { success: true, data: null };
      }
      const activeTab = activeTabResult.data;
      const matchingWorkspace = workspaces.find(
        (workspace) => workspace.websites.some(
          (website) => activeTab.url.startsWith(website.url)
        )
      );
      return { success: true, data: matchingWorkspace || null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to detect active workspace",
          details: error
        }
      };
    }
  }
  /**
   * 添加当前标签页到工作区
   */
  static async addCurrentTabToWorkspace(workspaceId) {
    try {
      const activeTabResult = await TabManager.getActiveTab();
      if (!activeTabResult.success) return activeTabResult;
      const activeTab = activeTabResult.data;
      const addResult = await WorkspaceManager.addWebsite(
        workspaceId,
        activeTab.url,
        {
          title: activeTab.title,
          favicon: activeTab.favicon,
          pinTab: activeTab.isPinned
        }
      );
      return addResult;
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to add current tab to workspace",
          details: error
        }
      };
    }
  }
  /**
   * 快速切换到下一个工作区
   */
  static async switchToNextWorkspace() {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) return workspacesResult;
      const workspaces = workspacesResult.data;
      if (workspaces.length === 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: "No workspaces available"
          }
        };
      }
      const currentResult = await this.getCurrentWorkspace();
      if (!currentResult.success) return currentResult;
      const currentWorkspace = currentResult.data;
      let nextIndex = 0;
      if (currentWorkspace) {
        const currentIndex = workspaces.findIndex((w) => w.id === currentWorkspace.id);
        nextIndex = (currentIndex + 1) % workspaces.length;
      }
      const nextWorkspace = workspaces[nextIndex];
      return await this.switchToWorkspace(nextWorkspace.id);
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to switch to next workspace",
          details: error
        }
      };
    }
  }
  /**
   * 清理工作区的隐藏窗口
   */
  static async cleanupWorkspaceHiddenWindow(workspaceId) {
    try {
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) return workspaceResult;
      const workspace = workspaceResult.data;
      if (workspace.hiddenWindowId) {
        console.log(`Cleaning up hidden window ${workspace.hiddenWindowId} for workspace: ${workspace.name}`);
        await WindowManager.closeHiddenWindow(workspace.hiddenWindowId);
        await this.updateWorkspaceHiddenWindowId(workspaceId, void 0);
      }
      return { success: true };
    } catch (error) {
      console.error("Failed to cleanup workspace hidden window:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to cleanup workspace hidden window",
          details: error
        }
      };
    }
  }
  /**
   * 清理所有工作区的隐藏窗口
   */
  static async cleanupAllHiddenWindows() {
    try {
      console.log("Cleaning up all workspace hidden windows...");
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) return workspacesResult;
      const workspaces = workspacesResult.data;
      for (const workspace of workspaces) {
        if (workspace.hiddenWindowId) {
          await this.cleanupWorkspaceHiddenWindow(workspace.id);
        }
      }
      await WindowManager.cleanupAllHiddenWindows();
      console.log("All hidden windows cleaned up");
      return { success: true };
    } catch (error) {
      console.error("Failed to cleanup all hidden windows:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to cleanup all hidden windows",
          details: error
        }
      };
    }
  }
}

class WorkspaceManager {
  /**
   * 生成唯一ID
   */
  static generateId() {
    return `ws_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  /**
   * 生成网站ID
   */
  static generateWebsiteId() {
    return `site_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  /**
   * 验证URL格式
   */
  static isValidUrl(url) {
    return URL_REGEX.test(url);
  }
  /**
   * 获取网站favicon
   */
  static async getFavicon(url) {
    try {
      const domain = new URL(url).origin;
      return `${domain}/favicon.ico`;
    } catch {
      return DEFAULT_FAVICON;
    }
  }
  /**
   * 获取网站标题
   */
  static async getWebsiteTitle(url) {
    try {
      const tabs = await chrome.tabs.query({ url });
      if (tabs.length > 0 && tabs[0].title) {
        return tabs[0].title;
      }
      const domain = new URL(url).hostname;
      return domain.replace("www.", "");
    } catch {
      return url;
    }
  }
  /**
   * 创建新工作区
   */
  static async createWorkspace(options) {
    try {
      if (!options.name.trim()) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.INVALID_URL,
            message: "Workspace name cannot be empty"
          }
        };
      }
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return {
          success: false,
          error: workspacesResult.error
        };
      }
      const existingWorkspaces = workspacesResult.data;
      if (existingWorkspaces.some((w) => w.name === options.name)) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.DUPLICATE_WORKSPACE,
            message: "Workspace with this name already exists"
          }
        };
      }
      const workspace = {
        id: this.generateId(),
        name: options.name,
        icon: options.icon || WORKSPACE_ICONS[Math.floor(Math.random() * WORKSPACE_ICONS.length)],
        color: options.color || WORKSPACE_COLORS[Math.floor(Math.random() * WORKSPACE_COLORS.length)],
        websites: [],
        createdAt: Date.now(),
        updatedAt: Date.now(),
        isActive: false,
        order: existingWorkspaces.length
      };
      if (options.websites) {
        for (let i = 0; i < options.websites.length; i++) {
          const siteData = options.websites[i];
          const website = {
            id: this.generateWebsiteId(),
            url: siteData.url,
            title: siteData.title || await this.getWebsiteTitle(siteData.url),
            favicon: siteData.favicon || await this.getFavicon(siteData.url),
            isPinned: siteData.isPinned,
            addedAt: Date.now(),
            order: i
          };
          workspace.websites.push(website);
        }
      }
      existingWorkspaces.push(workspace);
      const saveResult = await StorageManager.saveWorkspaces(existingWorkspaces);
      if (!saveResult.success) {
        return {
          success: false,
          error: saveResult.error
        };
      }
      if (options.activate) {
        await StorageManager.setActiveWorkspaceId(workspace.id);
        workspace.isActive = true;
      }
      return { success: true, data: workspace };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to create workspace",
          details: error
        }
      };
    }
  }
  /**
   * 更新工作区
   */
  static async updateWorkspace(id, options) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) return workspacesResult;
      const workspaces = workspacesResult.data;
      const workspaceIndex = workspaces.findIndex((w) => w.id === id);
      if (workspaceIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${id} not found`
          }
        };
      }
      const workspace = workspaces[workspaceIndex];
      if (options.name !== void 0) workspace.name = options.name;
      if (options.icon !== void 0) workspace.icon = options.icon;
      if (options.color !== void 0) workspace.color = options.color;
      if (options.websites !== void 0) workspace.websites = options.websites;
      if (options.isActive !== void 0) workspace.isActive = options.isActive;
      workspace.updatedAt = Date.now();
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) return saveResult;
      return { success: true, data: workspace };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to update workspace",
          details: error
        }
      };
    }
  }
  /**
   * 删除工作区
   */
  static async deleteWorkspace(id) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) return workspacesResult;
      const workspaces = workspacesResult.data;
      const workspaceIndex = workspaces.findIndex((w) => w.id === id);
      if (workspaceIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${id} not found`
          }
        };
      }
      try {
        await WorkspaceSwitcher.cleanupWorkspaceHiddenWindow(id);
      } catch (error) {
        console.error("Failed to cleanup hidden window during workspace deletion:", error);
      }
      workspaces.splice(workspaceIndex, 1);
      workspaces.forEach((workspace, index) => {
        workspace.order = index;
      });
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) return saveResult;
      const activeIdResult = await StorageManager.getActiveWorkspaceId();
      if (activeIdResult.success && activeIdResult.data === id) {
        await StorageManager.setActiveWorkspaceId(null);
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to delete workspace",
          details: error
        }
      };
    }
  }
  /**
   * 添加网站到工作区
   */
  static async addWebsite(workspaceId, url, options = {}) {
    try {
      if (!this.isValidUrl(url)) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.INVALID_URL,
            message: "Invalid URL format"
          }
        };
      }
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) return workspacesResult;
      const workspaces = workspacesResult.data;
      const workspace = workspaces.find((w) => w.id === workspaceId);
      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${workspaceId} not found`
          }
        };
      }
      if (workspace.websites.some((w) => w.url === url)) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.DUPLICATE_WEBSITE,
            message: "Website with this URL already exists in workspace"
          }
        };
      }
      const website = {
        id: this.generateWebsiteId(),
        url,
        title: options.title || await this.getWebsiteTitle(url),
        favicon: options.favicon || await this.getFavicon(url),
        isPinned: options.pinTab || false,
        addedAt: Date.now(),
        order: workspace.websites.length
      };
      workspace.websites.push(website);
      workspace.updatedAt = Date.now();
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) return saveResult;
      if (options.openInNewTab) {
        await chrome.tabs.create({
          url,
          pinned: options.pinTab || false
        });
      }
      return { success: true, data: website };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to add website",
          details: error
        }
      };
    }
  }
  /**
   * 从工作区移除网站
   */
  static async removeWebsite(workspaceId, websiteId) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) return workspacesResult;
      const workspaces = workspacesResult.data;
      const workspace = workspaces.find((w) => w.id === workspaceId);
      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${workspaceId} not found`
          }
        };
      }
      const websiteIndex = workspace.websites.findIndex((w) => w.id === websiteId);
      if (websiteIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WEBSITE_NOT_FOUND,
            message: `Website with id ${websiteId} not found`
          }
        };
      }
      workspace.websites.splice(websiteIndex, 1);
      workspace.websites.forEach((website, index) => {
        website.order = index;
      });
      workspace.updatedAt = Date.now();
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) return saveResult;
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to remove website",
          details: error
        }
      };
    }
  }
  /**
   * 更新工作区中的网站
   */
  static async updateWebsite(workspaceId, websiteId, updates) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return {
          success: false,
          error: workspacesResult.error
        };
      }
      const workspaces = workspacesResult.data;
      const workspace = workspaces.find((w) => w.id === workspaceId);
      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${workspaceId} not found`
          }
        };
      }
      const website = workspace.websites.find((w) => w.id === websiteId);
      if (!website) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WEBSITE_NOT_FOUND,
            message: `Website with id ${websiteId} not found`
          }
        };
      }
      if (updates.url !== void 0) {
        if (!this.isValidUrl(updates.url)) {
          return {
            success: false,
            error: {
              code: ERROR_CODES.INVALID_URL,
              message: "Invalid URL format"
            }
          };
        }
        website.url = updates.url;
        website.favicon = await this.getFavicon(updates.url);
      }
      if (updates.title !== void 0) {
        website.title = updates.title;
      }
      if (updates.isPinned !== void 0) {
        website.isPinned = updates.isPinned;
      }
      workspace.updatedAt = Date.now();
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) {
        return {
          success: false,
          error: saveResult.error
        };
      }
      return { success: true, data: website };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to update website",
          details: error
        }
      };
    }
  }
  /**
   * 重新排序工作区
   */
  static async reorderWorkspaces(workspaceIds) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) return workspacesResult;
      const workspaces = workspacesResult.data;
      const reorderedWorkspaces = [];
      workspaceIds.forEach((id, index) => {
        const workspace = workspaces.find((w) => w.id === id);
        if (workspace) {
          workspace.order = index;
          reorderedWorkspaces.push(workspace);
        }
      });
      const saveResult = await StorageManager.saveWorkspaces(reorderedWorkspaces);
      if (!saveResult.success) return saveResult;
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to reorder workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 重新排序工作区内的网站
   */
  static async reorderWebsites(workspaceId, websiteIds) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) return workspacesResult;
      const workspaces = workspacesResult.data;
      const workspace = workspaces.find((w) => w.id === workspaceId);
      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${workspaceId} not found`
          }
        };
      }
      const reorderedWebsites = [];
      websiteIds.forEach((id, index) => {
        const website = workspace.websites.find((w) => w.id === id);
        if (website) {
          website.order = index;
          reorderedWebsites.push(website);
        }
      });
      workspace.websites = reorderedWebsites;
      workspace.updatedAt = Date.now();
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) return saveResult;
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to reorder websites",
          details: error
        }
      };
    }
  }
}

export { COMMANDS as C, StorageManager as S, URL_REGEX as U, WorkspaceManager as W, WorkspaceSwitcher as a, WORKSPACE_ICONS as b, WORKSPACE_COLORS as c, WORKSPACE_TEMPLATES as d };
