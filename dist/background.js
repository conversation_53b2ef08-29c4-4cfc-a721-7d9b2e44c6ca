import { C as COMMANDS, a as WorkspaceSwitcher, S as StorageManager } from './assets/workspace-szC5_S3W.js';

class BackgroundService {
  constructor() {
    this.init();
  }
  /**
   * 初始化后台服务
   */
  async init() {
    await this.setupSidePanel();
    this.setupCommandListeners();
    this.setupTabListeners();
    this.setupStorageListeners();
    this.setupLifecycleListeners();
    await this.initializeDefaultData();
    await this.cleanupOnStartup();
    console.log("WorkSpace Pro background service initialized");
  }
  /**
   * 设置侧边栏
   */
  async setupSidePanel() {
    try {
      await chrome.sidePanel.setPanelBehavior({
        openPanelOnActionClick: true
      });
    } catch (error) {
      console.error("Failed to setup side panel:", error);
    }
  }
  /**
   * 设置命令监听器
   */
  setupCommandListeners() {
    chrome.commands.onCommand.addListener(async (command) => {
      console.log("Command received:", command);
      try {
        switch (command) {
          case COMMANDS.SWITCH_WORKSPACE_1:
            await this.switchToWorkspaceByIndex(0);
            break;
          case COMMANDS.SWITCH_WORKSPACE_2:
            await this.switchToWorkspaceByIndex(1);
            break;
          case COMMANDS.SWITCH_WORKSPACE_3:
            await this.switchToWorkspaceByIndex(2);
            break;
          case COMMANDS.TOGGLE_SIDEPANEL:
            await this.toggleSidePanel();
            break;
          default:
            console.log("Unknown command:", command);
        }
      } catch (error) {
        console.error("Error handling command:", command, error);
      }
    });
  }
  /**
   * 设置标签页监听器
   */
  setupTabListeners() {
    chrome.tabs.onActivated.addListener(async (_activeInfo) => {
      try {
        const detectedResult = await WorkspaceSwitcher.detectActiveWorkspace();
        if (detectedResult.success && detectedResult.data) {
          const currentResult = await WorkspaceSwitcher.getCurrentWorkspace();
          if (currentResult.success && (!currentResult.data || currentResult.data.id !== detectedResult.data.id)) {
            await WorkspaceSwitcher.switchToWorkspace(detectedResult.data.id, {
              closeOtherTabs: false,
              focusFirstTab: false
            });
          }
        }
      } catch (error) {
        console.error("Error handling tab activation:", error);
      }
    });
    chrome.tabs.onUpdated.addListener(async (_tabId, changeInfo, tab) => {
      if (changeInfo.status === "complete" && tab.url) {
        try {
          console.log("Tab updated:", tab.url);
        } catch (error) {
          console.error("Error handling tab update:", error);
        }
      }
    });
    chrome.tabs.onCreated.addListener(async (tab) => {
      try {
        console.log("Tab created:", tab.url);
      } catch (error) {
        console.error("Error handling tab creation:", error);
      }
    });
  }
  /**
   * 设置存储监听器
   */
  setupStorageListeners() {
    StorageManager.onChanged((changes) => {
      console.log("Storage changed:", changes);
      this.notifySidePanelUpdate(changes);
    });
  }
  /**
   * 设置扩展生命周期监听器
   */
  setupLifecycleListeners() {
    chrome.runtime.onStartup.addListener(async () => {
      console.log("Extension startup detected");
      await this.cleanupOnStartup();
    });
    chrome.runtime.onInstalled.addListener(async (details) => {
      console.log("Extension installed/updated:", details.reason);
      if (details.reason === "install" || details.reason === "update") {
        await this.cleanupOnStartup();
      }
    });
    chrome.runtime.onSuspend.addListener(async () => {
      console.log("Extension suspending, cleaning up...");
      await this.cleanupOnSuspend();
    });
  }
  /**
   * 初始化默认数据
   */
  async initializeDefaultData() {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (workspacesResult.success && workspacesResult.data.length === 0) {
        console.log("No workspaces found, creating default workspace templates");
      }
    } catch (error) {
      console.error("Error initializing default data:", error);
    }
  }
  /**
   * 根据索引切换工作区
   */
  async switchToWorkspaceByIndex(index) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        console.error("Failed to get workspaces:", workspacesResult.error);
        return;
      }
      const workspaces = workspacesResult.data;
      if (index >= 0 && index < workspaces.length) {
        const workspace = workspaces[index];
        const result = await WorkspaceSwitcher.switchToWorkspace(workspace.id);
        if (result.success) {
          console.log(`Switched to workspace: ${workspace.name}`);
          this.showNotification(`切换到工作区: ${workspace.name}`, workspace.icon);
        } else {
          console.error("Failed to switch workspace:", result.error);
        }
      } else {
        console.log(`No workspace at index ${index}`);
      }
    } catch (error) {
      console.error("Error switching workspace by index:", error);
    }
  }
  /**
   * 切换侧边栏显示状态
   */
  async toggleSidePanel() {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length > 0) {
        const tabId = tabs[0].id;
        console.log("Toggle side panel for tab:", tabId);
      }
    } catch (error) {
      console.error("Error toggling side panel:", error);
    }
  }
  /**
   * 显示通知
   */
  showNotification(message, icon) {
    try {
      chrome.notifications.create({
        type: "basic",
        iconUrl: "icons/icon-48.png",
        title: "WorkSpace Pro",
        message: `${icon || "🚀"} ${message}`
      });
      console.log("Notification shown:", message);
    } catch (error) {
      console.error("Error showing notification:", error);
    }
  }
  /**
   * 启动时清理
   */
  async cleanupOnStartup() {
    try {
      console.log("Performing startup cleanup...");
      await WorkspaceSwitcher.cleanupAllHiddenWindows();
      console.log("Startup cleanup completed");
    } catch (error) {
      console.error("Error during startup cleanup:", error);
    }
  }
  /**
   * 挂起时清理
   */
  async cleanupOnSuspend() {
    try {
      console.log("Performing suspend cleanup...");
      await WorkspaceSwitcher.cleanupAllHiddenWindows();
      console.log("Suspend cleanup completed");
    } catch (error) {
      console.error("Error during suspend cleanup:", error);
    }
  }
  /**
   * 通知侧边栏更新
   */
  notifySidePanelUpdate(_changes) {
    try {
      console.log("Notifying side panel of storage changes");
    } catch (error) {
      console.error("Error notifying side panel:", error);
    }
  }
}
new BackgroundService();
