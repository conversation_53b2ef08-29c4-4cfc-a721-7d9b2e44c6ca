# WorkSpace Pro - 隐藏窗口模式测试指南

## 🎯 新功能概述

WorkSpace Pro 现在支持真正的工作区隔离，使用隐藏窗口技术实现类似 Workona 的体验：

- **真正的状态保持**：标签页保持所有运行时状态、表单数据、滚动位置等
- **瞬时切换**：工作区切换感觉瞬时完成，无需重新加载
- **完全隔离**：每个工作区独立维护自己的标签页集合
- **用户透明**：隐藏窗口不在任务栏显示，不干扰用户工作流

## 🔧 技术实现

### 核心机制
1. **隐藏窗口创建**：为每个工作区创建最小化的离屏窗口（坐标 -3000, -3000）
2. **标签页移动**：使用 `chrome.tabs.move()` API 在主窗口和隐藏窗口间移动标签页
3. **状态保持**：标签页移动时保持所有状态（固定状态、活跃状态、运行时数据）
4. **自动清理**：工作区删除时自动清理对应的隐藏窗口

### 新增设置
- **使用隐藏窗口模式**：默认启用，可在设置中关闭回退到传统模式

## 🧪 测试步骤

### 1. 安装和基础设置

#### 重新安装扩展
1. 在 `chrome://extensions/` 中删除旧版本
2. 重新加载新构建的扩展（选择 `dist` 目录）
3. 确认扩展正常加载

#### 检查设置
1. 打开扩展侧边栏
2. 进入设置面板
3. 确认"使用隐藏窗口模式"已启用（默认）

### 2. 隐藏窗口模式核心测试

#### 测试场景 1：基础工作区切换
1. **准备工作**：
   - 创建工作区A，添加 Google、GitHub
   - 创建工作区B，添加 YouTube、Stack Overflow
   - 手动打开一些其他网站（如 Twitter、Reddit）

2. **测试步骤**：
   - 切换到工作区A
   - **预期结果**：
     - Google 和 GitHub 在新标签页中打开
     - 其他网站（Twitter、Reddit）消失但不关闭
     - 切换感觉瞬时完成

3. **验证隐藏窗口**：
   - 打开 Chrome 任务管理器（Shift+Esc）
   - 应该看到额外的 Chrome 进程（隐藏窗口）
   - 但在任务栏中看不到额外的窗口

#### 测试场景 2：状态保持验证
1. **准备工作**：
   - 在工作区A中打开一个有表单的网站（如 Gmail 登录页）
   - 填写一些表单数据但不提交
   - 滚动到页面中间位置

2. **测试步骤**：
   - 切换到工作区B
   - 再切换回工作区A
   - **预期结果**：
     - 表单数据完全保持
     - 滚动位置保持
     - 页面状态完全恢复

#### 测试场景 3：多次快速切换
1. **测试步骤**：
   - 在工作区A和B之间快速切换5-10次
   - **预期结果**：
     - 每次切换都很快（<500ms）
     - 标签页状态始终保持
     - 没有重复的标签页创建

### 3. 边界情况测试

#### 测试场景 4：手动关闭隐藏窗口
1. **测试步骤**：
   - 在 Chrome 任务管理器中手动结束隐藏窗口进程
   - 尝试切换回该工作区
   - **预期结果**：
     - 扩展应该检测到窗口丢失
     - 自动重新创建工作区网站
     - 不应该出现错误

#### 测试场景 5：工作区删除清理
1. **测试步骤**：
   - 创建一个工作区并切换到它（创建隐藏窗口）
   - 删除该工作区
   - 检查 Chrome 任务管理器
   - **预期结果**：
     - 对应的隐藏窗口应该被自动关闭
     - 没有遗留的隐藏窗口进程

#### 测试场景 6：扩展重启清理
1. **测试步骤**：
   - 创建多个工作区并切换（创建多个隐藏窗口）
   - 重新加载扩展
   - 检查 Chrome 任务管理器
   - **预期结果**：
     - 所有旧的隐藏窗口应该被清理
     - 重新切换工作区时创建新的隐藏窗口

### 4. 传统模式回退测试

#### 测试场景 7：禁用隐藏窗口模式
1. **测试步骤**：
   - 在设置中关闭"使用隐藏窗口模式"
   - 切换工作区
   - **预期结果**：
     - 回退到传统的标签页关闭/创建模式
     - "自动关闭其他标签页"等设置重新生效
     - 不创建隐藏窗口

### 5. 性能和稳定性测试

#### 测试场景 8：大量标签页测试
1. **测试步骤**：
   - 在一个工作区中打开20+个标签页
   - 切换到另一个工作区
   - 再切换回来
   - **预期结果**：
     - 所有标签页都能正确移动
     - 切换时间仍然合理（<2秒）
     - 没有标签页丢失

#### 测试场景 9：长时间运行测试
1. **测试步骤**：
   - 创建多个工作区，每个包含多个网站
   - 在工作区间频繁切换1小时
   - 监控内存使用和性能
   - **预期结果**：
     - 内存使用稳定，无明显泄漏
     - 切换速度保持一致
     - 扩展保持响应

## 🐛 调试和日志

### 查看详细日志
1. **前端日志**：
   - 右键侧边栏 → 检查 → Console
   - 查看工作区切换的详细日志

2. **后台日志**：
   - chrome://extensions/ → 扩展详情 → 检查视图
   - 查看隐藏窗口创建/清理日志

### 关键日志信息
- `Using hidden windows mode for workspace switch`
- `Creating hidden window...`
- `Moving X tabs to window Y`
- `Hidden window created with ID: X`
- `Cleaning up hidden window X`

## ✅ 验证检查清单

### 基础功能
- [ ] 隐藏窗口模式默认启用
- [ ] 工作区切换创建隐藏窗口
- [ ] 标签页正确移动到隐藏窗口
- [ ] 从隐藏窗口正确恢复标签页

### 状态保持
- [ ] 表单数据完全保持
- [ ] 滚动位置保持
- [ ] 页面运行时状态保持
- [ ] 标签页固定状态保持

### 性能体验
- [ ] 切换感觉瞬时完成（<500ms）
- [ ] 没有页面重新加载
- [ ] 内存使用合理
- [ ] 长时间运行稳定

### 清理机制
- [ ] 工作区删除时清理隐藏窗口
- [ ] 扩展重启时清理遗留窗口
- [ ] 手动关闭隐藏窗口时优雅处理

### 回退机制
- [ ] 可以禁用隐藏窗口模式
- [ ] 传统模式正常工作
- [ ] 设置项正确禁用/启用

## 🎯 成功标准

实现后的工作区切换应该：

1. **感觉瞬时**：切换在500ms内完成，无明显延迟
2. **状态完整**：所有标签页状态（表单、滚动、运行时）完全保持
3. **用户透明**：隐藏窗口不干扰用户，不在任务栏显示
4. **资源高效**：内存使用合理，无明显性能影响
5. **稳定可靠**：长时间使用无崩溃，自动清理机制完善

## 🚨 已知限制

1. **Chrome限制**：隐藏窗口仍会在任务管理器中显示
2. **内存使用**：每个工作区的隐藏窗口会占用额外内存
3. **标签页数量**：大量标签页移动可能需要更多时间
4. **扩展权限**：需要完整的标签页和窗口管理权限

---

**注意**：这是一个重大功能更新，建议在测试环境中充分验证后再在生产环境使用。
