# WorkSpace Pro - 开发指南

## 🏗️ 项目架构

### 技术栈
- **Chrome Extension**: Manifest V3
- **前端框架**: React 18 + TypeScript
- **样式**: Tailwind CSS
- **构建工具**: Vite
- **图标库**: Lucide React
- **拖拽功能**: @dnd-kit (预留)

### 目录结构
```
workspace-pro-chrome-extension/
├── public/
│   ├── manifest.json          # Chrome扩展配置
│   └── icons/                 # 扩展图标
├── src/
│   ├── background/            # 后台脚本
│   │   └── background.ts      # Service Worker
│   ├── sidepanel/            # 侧边栏应用
│   │   ├── App.tsx           # 主应用组件
│   │   └── main.tsx          # 入口文件
│   ├── components/           # React组件
│   │   ├── WorkspaceList.tsx # 工作区列表
│   │   ├── WorkspaceItem.tsx # 工作区项目
│   │   ├── WebsiteList.tsx   # 网站列表
│   │   └── ...               # 其他组件
│   ├── hooks/                # React Hooks
│   │   └── useWorkspaces.ts  # 工作区管理Hook
│   ├── utils/                # 工具函数
│   │   ├── storage.ts        # 存储管理
│   │   ├── workspace.ts      # 工作区操作
│   │   ├── tabs.ts           # 标签页管理
│   │   └── constants.ts      # 常量定义
│   ├── types/                # TypeScript类型
│   │   └── workspace.ts      # 数据模型
│   └── styles/               # 样式文件
│       └── globals.css       # 全局样式
├── dist/                     # 构建输出
└── sidepanel.html            # 侧边栏HTML
```

## 🚀 开发环境设置

### 前置要求
- Node.js 18+
- npm 或 yarn
- Chrome 浏览器 (版本 114+)

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 类型检查
```bash
npm run type-check
```

## 🔧 核心模块说明

### 1. 数据模型 (`src/types/workspace.ts`)

#### WorkSpace 接口
```typescript
interface WorkSpace {
  id: string;           // 唯一标识
  name: string;         // 工作区名称
  icon: string;         // 图标emoji
  color: string;        // 主题颜色
  websites: Website[];  // 网站列表
  createdAt: number;    // 创建时间
  updatedAt: number;    // 更新时间
  isActive: boolean;    // 是否活跃
  order: number;        // 排序
}
```

#### Website 接口
```typescript
interface Website {
  id: string;       // 唯一标识
  url: string;      // 网站URL
  title: string;    // 网站标题
  favicon: string;  // 网站图标
  isPinned: boolean; // 是否固定
  addedAt: number;  // 添加时间
  order: number;    // 排序
}
```

### 2. 存储管理 (`src/utils/storage.ts`)

#### 主要功能
- Chrome Storage API封装
- 数据持久化
- 导入导出功能
- 存储变化监听

#### 核心方法
```typescript
class StorageManager {
  static async getWorkspaces(): Promise<OperationResult<WorkSpace[]>>
  static async saveWorkspaces(workspaces: WorkSpace[]): Promise<OperationResult<void>>
  static async getSettings(): Promise<OperationResult<Settings>>
  static async saveSettings(settings: Partial<Settings>): Promise<OperationResult<void>>
  static async exportData(): Promise<OperationResult<string>>
  static async importData(jsonData: string): Promise<OperationResult<void>>
}
```

### 3. 工作区管理 (`src/utils/workspace.ts`)

#### 主要功能
- 工作区CRUD操作
- 网站管理
- 排序功能

#### 核心方法
```typescript
class WorkspaceManager {
  static async createWorkspace(options: CreateWorkspaceOptions): Promise<OperationResult<WorkSpace>>
  static async updateWorkspace(id: string, options: UpdateWorkspaceOptions): Promise<OperationResult<WorkSpace>>
  static async deleteWorkspace(id: string): Promise<OperationResult<void>>
  static async addWebsite(workspaceId: string, url: string, options?: AddWebsiteOptions): Promise<OperationResult<Website>>
  static async removeWebsite(workspaceId: string, websiteId: string): Promise<OperationResult<void>>
}
```

### 4. 标签页管理 (`src/utils/tabs.ts`)

#### 主要功能
- Chrome Tabs API封装
- 标签页检测和操作
- 工作区相关标签页管理

#### 核心方法
```typescript
class TabManager {
  static async getAllTabs(): Promise<OperationResult<TabInfo[]>>
  static async getActiveTab(): Promise<OperationResult<TabInfo>>
  static async createTab(url: string, pinned?: boolean): Promise<OperationResult<TabInfo>>
  static async activateTab(tabId: number): Promise<OperationResult<void>>
  static async closeTab(tabId: number): Promise<OperationResult<void>>
}
```

### 5. 工作区切换 (`src/utils/workspaceSwitcher.ts`)

#### 主要功能
- 智能工作区切换
- 标签页自动管理
- 工作区检测

#### 核心方法
```typescript
class WorkspaceSwitcher {
  static async switchToWorkspace(workspaceId: string, options?: WorkspaceSwitchOptions): Promise<OperationResult<void>>
  static async getCurrentWorkspace(): Promise<OperationResult<WorkSpace | null>>
  static async detectActiveWorkspace(): Promise<OperationResult<WorkSpace | null>>
  static async addCurrentTabToWorkspace(workspaceId: string): Promise<OperationResult<void>>
}
```

## 🎨 UI组件说明

### 1. 主应用 (`src/sidepanel/App.tsx`)
- 应用入口组件
- 状态管理
- 模态框控制

### 2. 工作区列表 (`src/components/WorkspaceList.tsx`)
- 工作区展示
- 展开/折叠控制
- 拖拽排序支持

### 3. 工作区项目 (`src/components/WorkspaceItem.tsx`)
- 单个工作区展示
- 操作菜单
- 网站列表集成

### 4. 网站列表 (`src/components/WebsiteList.tsx`)
- 网站项目展示
- 点击打开功能
- 删除操作

### 5. 模态框组件
- `CreateWorkspaceModal`: 创建工作区
- `EditWorkspaceModal`: 编辑工作区
- `AddWebsiteModal`: 添加网站
- `SettingsPanel`: 设置面板
- `ConfirmDialog`: 确认对话框

## 🔌 Chrome Extension API

### 权限配置
```json
{
  "permissions": [
    "sidePanel",    // 侧边栏API
    "tabs",         // 标签页管理
    "storage",      // 本地存储
    "activeTab"     // 活跃标签页
  ],
  "host_permissions": [
    "<all_urls>"    // 所有网站访问
  ]
}
```

### 后台脚本 (`src/background/background.ts`)
- Service Worker
- 命令监听
- 标签页事件处理
- 存储变化监听

### 侧边栏集成
- Side Panel API
- 自动打开配置
- 响应式设计

## 🧪 测试策略

### 功能测试
1. **工作区管理**
   - 创建、编辑、删除工作区
   - 工作区排序
   - 工作区切换

2. **网站管理**
   - 添加、删除网站
   - 网站排序
   - 标签页打开

3. **标签页管理**
   - 自动标签页检测
   - 标签页固定/取消固定
   - 标签页关闭

4. **数据持久化**
   - 设置保存/加载
   - 数据导入/导出
   - 存储变化同步

### 性能测试
- 大量工作区处理
- 标签页操作响应时间
- 内存使用优化

### 兼容性测试
- Chrome版本兼容性
- 操作系统兼容性
- 不同网站兼容性

## 🚀 部署流程

### 开发版本
1. 构建项目：`npm run build`
2. 加载到Chrome扩展管理页面
3. 测试功能完整性

### 生产版本
1. 版本号更新
2. 完整测试
3. 打包上传到Chrome Web Store
4. 发布说明编写

## 🐛 调试技巧

### 1. 后台脚本调试
- 在 `chrome://extensions/` 中点击"检查视图"
- 查看Service Worker控制台

### 2. 侧边栏调试
- 右键侧边栏 → 检查
- 使用React DevTools

### 3. 存储调试
- Chrome DevTools → Application → Storage
- 查看chrome.storage.local数据

### 4. 权限问题
- 检查manifest.json权限配置
- 确认host_permissions设置

## 📝 代码规范

### TypeScript
- 严格类型检查
- 接口优先于类型别名
- 明确的返回类型

### React
- 函数组件优先
- Hooks使用规范
- Props类型定义

### 样式
- Tailwind CSS类名
- 组件级样式隔离
- 响应式设计

### 命名规范
- 文件名：PascalCase (组件) / camelCase (工具)
- 变量名：camelCase
- 常量名：UPPER_SNAKE_CASE
- 接口名：PascalCase

## 🔄 版本管理

### 版本号规则
- 主版本号：重大功能变更
- 次版本号：新功能添加
- 修订号：Bug修复

### 发布流程
1. 功能开发完成
2. 测试验证
3. 版本号更新
4. 构建打包
5. 发布部署

---

**注意**: 开发过程中请遵循Chrome Extension开发最佳实践，确保扩展的安全性和性能。
