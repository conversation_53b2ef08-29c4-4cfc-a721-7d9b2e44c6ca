# WorkSpace Pro - 测试指南

## 🧪 功能测试步骤

### 1. 安装和基础测试

#### 安装扩展
1. 运行 `npm run build` 构建项目
2. 打开Chrome浏览器，进入 `chrome://extensions/`
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目的 `dist` 目录
6. 确认扩展出现在列表中且已启用

#### 基础功能测试
1. **打开侧边栏**
   - 点击扩展图标
   - 使用快捷键 `Ctrl+Shift+W`
   - 确认侧边栏正常显示

2. **界面检查**
   - 确认暗色主题正确应用
   - 检查所有图标和文字显示正常
   - 确认滚动条样式正确

### 2. 工作区管理测试

#### 创建工作区
1. 点击"新建工作区"按钮
2. 输入工作区名称（如："测试工作区"）
3. 选择不同的图标和颜色
4. 点击"创建工作区"
5. **预期结果**: 工作区出现在列表中

#### 使用预设模板
1. 在创建工作区时点击"显示模板"
2. 选择"AI工具集"模板
3. 确认名称、图标、颜色自动填充
4. 创建工作区
5. **预期结果**: 工作区包含预设的网站

#### 编辑工作区
1. 点击工作区的"更多操作"按钮（三个点）
2. 选择"编辑工作区"
3. 修改名称、图标或颜色
4. 保存更改
5. **预期结果**: 更改立即生效

#### 删除工作区
1. 点击工作区的"更多操作"按钮
2. 选择"删除工作区"
3. 确认删除操作
4. **预期结果**: 工作区从列表中消失

### 3. 网站管理测试

#### 添加当前标签页
1. 打开一个网站（如：https://google.com）
2. 在工作区中点击"添加当前标签页"
3. **预期结果**: 网站出现在工作区的网站列表中

#### 手动添加网站
1. 点击工作区的"更多操作" → "添加网站URL"
2. 输入网站URL（如：https://github.com）
3. 点击"添加网站"
4. **预期结果**: 网站出现在列表中

#### 删除网站
1. 在网站项目上悬停
2. 点击删除按钮（X）
3. **预期结果**: 网站从列表中移除

### 4. 标签页管理测试（核心功能）

#### 工作区切换测试
1. **准备工作**:
   - 创建一个包含2-3个网站的工作区
   - 确保这些网站当前没有在浏览器中打开

2. **切换到工作区**:
   - 点击工作区名称进行切换
   - **预期结果**: 
     - 工作区中的所有网站应该在新标签页中打开
     - 所有新打开的标签页应该被固定（pinned）
     - 工作区显示为"活跃"状态

3. **已存在标签页测试**:
   - 手动打开工作区中的一个网站
   - 再次切换到该工作区
   - **预期结果**:
     - 已存在的标签页应该被激活和固定
     - 不应该创建重复的标签页

4. **域名匹配测试**:
   - 打开 https://github.com/user/repo
   - 工作区中有 https://github.com
   - 切换工作区
   - **预期结果**: 应该识别为同一网站并固定现有标签页

#### 标签页固定测试
1. 切换到工作区后检查标签页
2. **预期结果**: 工作区相关的标签页应该显示固定图标（📌）

#### 快捷键测试
1. 使用 `Ctrl+Shift+1` 切换到第一个工作区
2. 使用 `Ctrl+Shift+2` 切换到第二个工作区
3. **预期结果**: 快捷键应该正常工作

### 5. 设置功能测试

#### 打开设置
1. 点击右上角的设置按钮
2. **预期结果**: 设置面板正常打开

#### 标签页管理设置
1. 切换"自动关闭其他标签页"选项
2. 切换"保留用户手动打开的标签页"选项
3. 保存设置
4. **预期结果**: 设置应该被保存并影响后续的工作区切换行为

#### 数据导出/导入
1. 点击"导出数据"
2. **预期结果**: 下载JSON文件
3. 点击"导入数据"并选择刚才的文件
4. **预期结果**: 数据应该正确导入

### 6. 错误处理测试

#### 无效URL测试
1. 尝试添加无效的URL（如："not-a-url"）
2. **预期结果**: 应该显示错误提示

#### 权限测试
1. 尝试访问chrome://扩展页面
2. 添加到工作区
3. **预期结果**: 应该被正确过滤或显示警告

#### 网络错误测试
1. 添加一个不存在的网站
2. 切换工作区
3. **预期结果**: 应该优雅地处理错误，不影响其他网站

### 7. 性能测试

#### 大量工作区测试
1. 创建10个以上的工作区
2. 每个工作区添加5-10个网站
3. 测试切换性能
4. **预期结果**: 界面应该保持响应

#### 内存使用测试
1. 长时间使用扩展
2. 频繁切换工作区
3. 检查Chrome任务管理器中的内存使用
4. **预期结果**: 内存使用应该稳定

### 8. 调试和日志

#### 查看控制台日志
1. 右键侧边栏 → "检查"
2. 查看Console标签
3. 进行各种操作时观察日志输出
4. **预期结果**: 应该看到详细的操作日志

#### 后台脚本调试
1. 在 `chrome://extensions/` 中点击扩展的"检查视图"
2. 查看Service Worker的控制台
3. **预期结果**: 应该看到后台操作的日志

### 9. 兼容性测试

#### 不同网站测试
- Google.com
- GitHub.com
- YouTube.com
- 本地开发服务器（如localhost:3000）

#### 特殊URL测试
- 带参数的URL
- 带锚点的URL
- 重定向的URL

### 10. 用户体验测试

#### 首次使用体验
1. 清除扩展数据
2. 模拟首次使用
3. **预期结果**: 应该显示友好的欢迎界面

#### 空状态处理
1. 删除所有工作区
2. **预期结果**: 应该显示创建工作区的引导

## 🐛 常见问题排查

### 标签页没有固定
1. 检查浏览器控制台是否有错误
2. 确认扩展有tabs权限
3. 检查URL是否正确匹配

### 工作区切换无响应
1. 检查后台脚本是否正常运行
2. 查看Service Worker控制台
3. 重新加载扩展

### 数据丢失
1. 检查chrome.storage.local中的数据
2. 确认没有清除浏览器数据
3. 尝试导入备份文件

## ✅ 测试检查清单

- [ ] 扩展正常安装和启用
- [ ] 侧边栏正常打开和关闭
- [ ] 工作区创建、编辑、删除功能正常
- [ ] 网站添加、删除功能正常
- [ ] **工作区切换时标签页正确固定**
- [ ] 快捷键功能正常
- [ ] 设置保存和加载正常
- [ ] 数据导出导入功能正常
- [ ] 错误处理正确
- [ ] 性能表现良好
- [ ] 用户体验友好

---

**重要**: 特别关注标签页管理功能，这是扩展的核心价值。如果发现问题，请查看浏览器控制台的详细日志。
