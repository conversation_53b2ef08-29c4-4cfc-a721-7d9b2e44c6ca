# WorkSpace Pro - 安装指南

## 🚀 快速安装

### 方法一：开发者模式安装（推荐）

1. **构建扩展**
   ```bash
   npm install
   npm run build
   ```

2. **打开Chrome扩展管理页面**
   - 在Chrome浏览器中输入：`chrome://extensions/`
   - 或者：菜单 → 更多工具 → 扩展程序

3. **启用开发者模式**
   - 点击右上角的"开发者模式"开关

4. **加载扩展**
   - 点击"加载已解压的扩展程序"
   - 选择项目的 `dist` 文件夹

5. **完成安装**
   - 扩展将出现在扩展列表中
   - 点击扩展图标或使用快捷键 `Ctrl+Shift+W` 打开侧边栏

### 方法二：打包安装

1. **打包扩展**
   ```bash
   npm run build
   ```

2. **创建扩展包**
   - 在Chrome扩展管理页面点击"打包扩展程序"
   - 选择 `dist` 文件夹作为扩展根目录
   - 生成 `.crx` 文件

3. **安装扩展包**
   - 将 `.crx` 文件拖拽到Chrome扩展管理页面
   - 确认安装

## ⚙️ 系统要求

- **Chrome浏览器**: 版本 114 或更高
- **操作系统**: Windows, macOS, Linux
- **权限要求**:
  - 侧边栏 (sidePanel)
  - 标签页管理 (tabs)
  - 本地存储 (storage)
  - 活跃标签页 (activeTab)
  - 所有网站访问 (host_permissions)

## 🎯 首次使用

### 1. 打开侧边栏
- 点击浏览器工具栏中的 WorkSpace Pro 图标
- 或使用快捷键 `Ctrl+Shift+W`

### 2. 创建第一个工作区
- 点击"新建工作区"按钮
- 输入工作区名称（如："AI工具"）
- 选择图标和颜色
- 点击"创建工作区"

### 3. 添加网站
- 在工作区中点击"添加当前标签页"
- 或点击"添加网站URL"手动输入网址
- 支持拖拽排序

### 4. 切换工作区
- 点击工作区名称切换
- 使用快捷键 `Ctrl+Shift+1/2/3` 快速切换
- 系统会自动管理相关标签页

## 🔧 配置选项

### 标签页管理
- **自动关闭其他标签页**: 切换工作区时关闭无关标签页
- **保留用户标签页**: 保留手动打开的标签页

### 界面设置
- **显示网站图标**: 在网站列表中显示favicon
- **删除前确认**: 删除工作区或网站前显示确认对话框

### 快捷键
- `Ctrl+Shift+W`: 切换侧边栏
- `Ctrl+Shift+1`: 切换到工作区1
- `Ctrl+Shift+2`: 切换到工作区2
- `Ctrl+Shift+3`: 切换到工作区3

## 📊 数据管理

### 导出数据
1. 打开设置面板
2. 点击"导出数据"
3. 保存JSON文件作为备份

### 导入数据
1. 打开设置面板
2. 点击"导入数据"
3. 选择之前导出的JSON文件

### 清除数据
1. 打开设置面板
2. 点击"清除所有数据"
3. 确认操作（不可撤销）

## 🐛 故障排除

### 扩展无法加载
- 确保Chrome版本 ≥ 114
- 检查开发者模式是否启用
- 重新构建项目：`npm run build`

### 侧边栏无法打开
- 检查扩展是否已启用
- 尝试刷新页面后重新打开
- 检查浏览器控制台是否有错误信息

### 标签页管理异常
- 检查扩展权限是否完整
- 确认网站URL格式正确
- 重启浏览器后重试

### 数据丢失
- 检查本地存储是否被清除
- 尝试导入之前的备份文件
- 重新创建工作区配置

## 📞 技术支持

如果遇到问题，请：

1. 查看浏览器控制台错误信息
2. 检查扩展是否为最新版本
3. 尝试重新安装扩展
4. 联系开发者获取支持

## 🔄 更新扩展

### 开发版本更新
1. 拉取最新代码
2. 运行 `npm install` 更新依赖
3. 运行 `npm run build` 重新构建
4. 在扩展管理页面点击"重新加载"

### 发布版本更新
- Chrome Web Store会自动更新
- 或手动检查更新并重新安装

---

**注意**: 这是一个开发版本的扩展，建议在测试环境中使用。生产环境使用前请充分测试所有功能。
