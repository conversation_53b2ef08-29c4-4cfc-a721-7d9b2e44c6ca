# WorkSpace Pro - Chrome工作区管理扩展

## 项目概述

WorkSpace Pro 是一个专业的Chrome标签页工作区管理扩展，专为AI工具重度使用者和技术工作者设计。使用Manifest V3和Side Panel API，提供智能的标签页组织解决方案。

## 功能特性

- 🚀 **多工作区支持** - 创建、编辑、删除多个工作区
- 📋 **智能标签页管理** - 自动检测、固定、切换标签页
- 🎨 **现代化UI** - 暗色主题，渐变色设计
- ⚡ **快捷键支持** - 快速切换工作区
- 💾 **数据持久化** - 本地存储工作区配置
- 🔄 **拖拽排序** - 直观的网址管理

## 技术栈

- **Manifest V3** - Chrome扩展最新标准
- **React 18** + **TypeScript** - 现代化前端开发
- **Tailwind CSS** - 实用优先的CSS框架
- **Vite** - 快速构建工具
- **Side Panel API** - Chrome侧边栏集成

## 开发环境设置

### 前置要求

- Node.js 18+
- Chrome 浏览器 (版本 114+)

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 类型检查

```bash
npm run type-check
```

## 项目结构

```
workspace-pro-chrome-extension/
├── public/
│   ├── manifest.json          # Chrome扩展配置
│   └── icons/                 # 扩展图标
├── src/
│   ├── background/            # 后台脚本
│   ├── sidepanel/            # 侧边栏主应用
│   ├── components/           # React组件
│   ├── utils/                # 工具函数
│   ├── types/                # TypeScript类型定义
│   └── hooks/                # React Hooks
├── sidepanel.html            # 侧边栏HTML入口
└── dist/                     # 构建输出目录
```

## 🚀 快速开始

### 安装扩展

详细安装说明请查看 [INSTALLATION.md](./INSTALLATION.md)

**快速安装步骤：**
1. 运行 `npm install && npm run build` 构建项目
2. 打开Chrome浏览器，进入 `chrome://extensions/`
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目的 `dist` 目录

### 使用说明

#### 快捷键
- `Ctrl+Shift+W` - 切换侧边栏
- `Ctrl+Shift+1/2/3` - 快速切换工作区

#### 基本操作
1. 点击扩展图标或使用快捷键打开侧边栏
2. 创建新工作区并添加网址
3. 切换工作区时自动管理标签页
4. 支持拖拽排序调整网址顺序

#### 预设模板
扩展内置了多个工作区模板：
- 🤖 **AI工具集** - ChatGPT, Claude, Gemini
- 💻 **开发环境** - GitHub, Stack Overflow, MDN
- 🎨 **设计工具** - Figma, Dribbble, Behance
- 🔬 **学术研究** - arXiv, Google Scholar, ResearchGate
- ⚡ **生产力工具** - Notion, Trello, Google Calendar

## 📚 文档

- [安装指南](./INSTALLATION.md) - 详细的安装和配置说明
- [开发指南](./DEVELOPMENT.md) - 完整的开发文档和API说明

## 🛠️ 开发指南

详细的开发文档请查看 [DEVELOPMENT.md](./DEVELOPMENT.md)

### 核心技术

- **Chrome Extension API**: Manifest V3, Side Panel, Tabs, Storage
- **前端技术**: React 18, TypeScript, Tailwind CSS
- **构建工具**: Vite, PostCSS
- **开发工具**: ESLint, TypeScript Compiler

### 快速开发

```bash
# 安装依赖
npm install

# 开发模式
npm run dev

# 构建生产版本
npm run build

# 类型检查
npm run type-check
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 更新日志

### v1.0.0
- 初始版本发布
- 基础工作区管理功能
- 侧边栏UI界面
- 标签页智能管理
